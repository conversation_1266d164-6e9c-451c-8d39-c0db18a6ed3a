'use client'

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { LoadingPage } from '@/components/ui/loading'

interface NavigationContextType {
  isNavigating: boolean
  navigationText: string
  startNavigation: (text?: string) => void
  finishNavigation: () => void
  navigateWithLoading: (path: string, text?: string) => void
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined)

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const [isNavigating, setIsNavigating] = useState(false)
  const [navigationText, setNavigationText] = useState('')
  const pathname = usePathname()
  const router = useRouter()
  const [previousPathname, setPreviousPathname] = useState(pathname)

  const startNavigation = useCallback((text?: string) => {
    setNavigationText(text || 'جاري تحميل الصفحة...')
    setIsNavigating(true)
  }, [])

  const finishNavigation = useCallback(() => {
    setIsNavigating(false)
    setNavigationText('')
  }, [])

  const navigateWithLoading = useCallback((path: string, text?: string) => {
    if (path === pathname) return // Don't navigate to same page
    
    startNavigation(text)
    
    // Add a small delay to show loading state
    setTimeout(() => {
      router.push(path)
    }, 100)
  }, [pathname, router, startNavigation])

  // Auto-finish navigation when pathname changes
  useEffect(() => {
    if (pathname !== previousPathname) {
      // Small delay to ensure smooth transition
      const timer = setTimeout(() => {
        finishNavigation()
        setPreviousPathname(pathname)
      }, 300)

      return () => clearTimeout(timer)
    }
  }, [pathname, previousPathname, finishNavigation])

  // Safety timeout to prevent stuck loading
  useEffect(() => {
    if (isNavigating) {
      const timeout = setTimeout(() => {
        console.warn('Navigation timeout - forcing finish')
        finishNavigation()
      }, 5000) // 5 second timeout

      return () => clearTimeout(timeout)
    }
  }, [isNavigating, finishNavigation])

  const value: NavigationContextType = {
    isNavigating,
    navigationText,
    startNavigation,
    finishNavigation,
    navigateWithLoading
  }

  return (
    <NavigationContext.Provider value={value}>
      {children}
      {isNavigating && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <LoadingPage text={navigationText} variant="dots" />
        </div>
      )}
    </NavigationContext.Provider>
  )
}

export function useNavigation() {
  const context = useContext(NavigationContext)
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider')
  }
  return context
}
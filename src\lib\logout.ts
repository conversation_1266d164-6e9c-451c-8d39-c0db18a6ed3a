import { createClient } from './supabase/client'

export async function performLogout() {
  try {
    const supabase = createClient()
    
    // Sign out from Supabase
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      console.error('Error signing out:', error)
    }
    
    // Clear any local storage or session storage if needed
    if (typeof window !== 'undefined') {
      localStorage.clear()
      sessionStorage.clear()
    }
    
    // Redirect to login page
    window.location.href = '/login'
    
  } catch (error) {
    console.error('Unexpected error during logout:', error)
    // Force redirect even if there's an error
    window.location.href = '/login'
  }
}

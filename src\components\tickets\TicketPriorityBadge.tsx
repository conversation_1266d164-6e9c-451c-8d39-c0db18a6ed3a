'use client'

import { Badge } from '@/components/ui/badge'

interface TicketPriorityBadgeProps {
  priority: 'low' | 'medium' | 'high'
  className?: string
}

export function TicketPriorityBadge({ priority, className }: TicketPriorityBadgeProps) {
  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case 'high':
        return {
          label: 'عالية',
          variant: 'destructive' as const,
        }
      case 'medium':
        return {
          label: 'متوسطة',
          variant: 'default' as const,
        }
      case 'low':
        return {
          label: 'منخفضة',
          variant: 'secondary' as const,
        }
      default:
        return {
          label: 'غير معروف',
          variant: 'outline' as const,
        }
    }
  }

  const config = getPriorityConfig(priority)

  return (
    <Badge variant={config.variant} className={className}>
      {config.label}
    </Badge>
  )
}

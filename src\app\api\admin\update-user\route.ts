import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { requireSystemAdmin } from '@/lib/auth'
import { UserRole, ROLE_HIERARCHY } from '@/lib/supabase'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function PUT(request: NextRequest) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { userId, full_name, phone, role } = await request.json()

    // Validate input
    if (!userId) {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    // Validate role
    const userRole = (role || 'sales_employee') as UserRole
    if (!ROLE_HIERARCHY[userRole]) {
      return NextResponse.json(
        { error: 'دور غير صحيح' },
        { status: 400 }
      )
    }

    // Update profile with role and role_level
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({
        full_name: full_name || null,
        phone: phone || null,
        role: userRole,
        role_level: ROLE_HIERARCHY[userRole].level
      })
      .eq('id', userId)

    if (updateError) {
      return NextResponse.json(
        { error: updateError.message },
        { status: 400 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

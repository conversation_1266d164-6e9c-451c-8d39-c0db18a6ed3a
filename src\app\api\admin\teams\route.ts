import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { requireSystemAdmin } from '@/lib/auth'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// GET - List all teams or teams by area
export async function GET(request: NextRequest) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { searchParams } = new URL(request.url)
    const areaId = searchParams.get('area_id')

    let query = supabaseAdmin
      .from('teams')
      .select(`
        *,
        area:areas!teams_area_id_fkey(id, name),
        manager:profiles!teams_manager_id_fkey(id, full_name, email),
        members:profiles!profiles_team_id_fkey(id, full_name, email, role)
      `)
      .order('created_at', { ascending: false })

    // Filter by area if specified
    if (areaId) {
      query = query.eq('area_id', areaId)
    }

    const { data: teams, error } = await query

    if (error) {
      console.error('Error fetching teams:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب الفرق' },
        { status: 500 }
      )
    }

    return NextResponse.json({ teams })

  } catch (error: any) {
    console.error('Teams API error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - Create new team
export async function POST(request: NextRequest) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { name, description, area_id, manager_id } = await request.json()

    // Validate required fields
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'اسم الفريق مطلوب' },
        { status: 400 }
      )
    }

    if (!area_id) {
      return NextResponse.json(
        { error: 'المنطقة مطلوبة' },
        { status: 400 }
      )
    }

    // Validate area exists
    const { data: area, error: areaError } = await supabaseAdmin
      .from('areas')
      .select('id, name')
      .eq('id', area_id)
      .single()

    if (areaError || !area) {
      return NextResponse.json(
        { error: 'المنطقة المحددة غير موجودة' },
        { status: 400 }
      )
    }

    // Validate manager if provided
    if (manager_id) {
      const { data: manager, error: managerError } = await supabaseAdmin
        .from('profiles')
        .select('id, role')
        .eq('id', manager_id)
        .single()

      if (managerError || !manager) {
        return NextResponse.json(
          { error: 'مدير الفريق غير موجود' },
          { status: 400 }
        )
      }

      // Check if manager has appropriate role
      if (!['system_admin', 'area_manager', 'team_manager'].includes(manager.role)) {
        return NextResponse.json(
          { error: 'المستخدم المحدد لا يملك صلاحية إدارة الفرق' },
          { status: 400 }
        )
      }
    }

    // Create team
    const { data: team, error } = await supabaseAdmin
      .from('teams')
      .insert({
        name: name.trim(),
        description: description?.trim() || null,
        area_id,
        manager_id: manager_id || null
      })
      .select(`
        *,
        area:areas(id, name),
        manager:profiles!manager_id(id, full_name, email)
      `)
      .single()

    if (error) {
      console.error('Error creating team:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في إنشاء الفريق' },
        { status: 500 }
      )
    }

    // Update manager's team_id if manager was assigned
    if (manager_id) {
      await supabaseAdmin
        .from('profiles')
        .update({ team_id: team.id })
        .eq('id', manager_id)
    }

    return NextResponse.json({ team })

  } catch (error: any) {
    console.error('Create team error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

"use client"

import * as React from "react"
import * as SwitchPrimitive from "@radix-ui/react-switch"

import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitive.Root>
>(({ className, ...props }, ref) => {
  const [isRTL, setIsRTL] = React.useState(false)
  
  React.useEffect(() => {
    const checkRTL = () => {
      const htmlDir = document.documentElement.dir
      const bodyDir = document.body?.dir
      const computedDir = window.getComputedStyle(document.documentElement).direction
      return htmlDir === 'rtl' || bodyDir === 'rtl' || computedDir === 'rtl'
    }
    
    setIsRTL(checkRTL())
  }, [])

  return (
    <SwitchPrimitive.Root
      className={cn(
        "peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-gray-300 dark:data-[state=unchecked]:bg-gray-600",
        className
      )}
      {...props}
      ref={ref}
    >
      <SwitchPrimitive.Thumb
        className={cn(
          "pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform duration-200 ease-in-out"
        )}
        style={{
          transform: props.checked 
            ? isRTL 
              ? 'translateX(-1.25rem)' 
              : 'translateX(1.25rem)'
            : 'translateX(0)'
        }}
      />
    </SwitchPrimitive.Root>
  )
})
Switch.displayName = SwitchPrimitive.Root.displayName

export { Switch }

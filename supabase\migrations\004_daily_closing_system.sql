-- Create areas table
CREATE TABLE IF NOT EXISTS public.areas (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    manager_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create teams table
CREATE TABLE IF NOT EXISTS public.teams (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    area_id UUID REFERENCES public.areas(id) ON DELETE CASCADE NOT NULL,
    manager_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create packages table
CREATE TABLE IF NOT EXISTS public.packages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create daily_closings table
CREATE TABLE IF NOT EXISTS public.daily_closings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    closing_date DATE NOT NULL,
    attendance_submitted BOOLEAN DEFAULT false,
    sales_submitted BOOLEAN DEFAULT false,
    departure_submitted BOOLEAN DEFAULT false,
    total_sales_amount DECIMAL(10,2) DEFAULT 0,
    cash_delivered DECIMAL(10,2) DEFAULT 0,
    cash_confirmed BOOLEAN DEFAULT false,
    deficit_amount DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(user_id, closing_date)
);

-- Create attendance_records table
CREATE TABLE IF NOT EXISTS public.attendance_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    daily_closing_id UUID REFERENCES public.daily_closings(id) ON DELETE CASCADE NOT NULL,
    check_in_time TIME NOT NULL,
    check_out_time TIME,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create sales_records table
CREATE TABLE IF NOT EXISTS public.sales_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    daily_closing_id UUID REFERENCES public.daily_closings(id) ON DELETE CASCADE NOT NULL,
    package_id UUID REFERENCES public.packages(id) ON DELETE RESTRICT NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Add area_id and team_id to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS area_id UUID REFERENCES public.areas(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS team_id UUID REFERENCES public.teams(id) ON DELETE SET NULL;

-- Create updated_at triggers for new tables
CREATE TRIGGER handle_areas_updated_at
    BEFORE UPDATE ON public.areas
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_teams_updated_at
    BEFORE UPDATE ON public.teams
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_packages_updated_at
    BEFORE UPDATE ON public.packages
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_daily_closings_updated_at
    BEFORE UPDATE ON public.daily_closings
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_attendance_records_updated_at
    BEFORE UPDATE ON public.attendance_records
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_sales_records_updated_at
    BEFORE UPDATE ON public.sales_records
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Enable Row Level Security
ALTER TABLE public.areas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_closings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.attendance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sales_records ENABLE ROW LEVEL SECURITY;

-- RLS Policies for areas table
-- System admins can do everything
CREATE POLICY "System admins can manage areas" ON public.areas
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- Area managers can view their own area
CREATE POLICY "Area managers can view own area" ON public.areas
    FOR SELECT USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role IN ('system_admin', 'area_manager')
        )
    );

-- RLS Policies for teams table
-- System admins and area managers can manage teams
CREATE POLICY "Admins and area managers can manage teams" ON public.teams
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            LEFT JOIN public.areas a ON p.area_id = a.id
            WHERE p.id = auth.uid() AND (
                p.role = 'system_admin' OR
                (p.role = 'area_manager' AND a.manager_id = auth.uid()) OR
                (p.role = 'team_manager' AND teams.manager_id = auth.uid())
            )
        )
    );

-- Team members can view their team
CREATE POLICY "Team members can view own team" ON public.teams
    FOR SELECT USING (
        id IN (SELECT team_id FROM public.profiles WHERE id = auth.uid()) OR
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.profiles p
            LEFT JOIN public.areas a ON p.area_id = a.id
            WHERE p.id = auth.uid() AND (
                p.role IN ('system_admin', 'area_manager') OR
                (p.role = 'area_manager' AND a.manager_id = auth.uid())
            )
        )
    );

-- RLS Policies for packages table
-- System admins can manage packages
CREATE POLICY "System admins can manage packages" ON public.packages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- All authenticated users can view active packages
CREATE POLICY "Users can view active packages" ON public.packages
    FOR SELECT USING (is_active = true AND auth.uid() IS NOT NULL);

-- RLS Policies for daily_closings table
-- Users can manage their own daily closings
CREATE POLICY "Users can manage own daily closings" ON public.daily_closings
    FOR ALL USING (user_id = auth.uid());

-- Managers can view their team/area daily closings
CREATE POLICY "Managers can view team daily closings" ON public.daily_closings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            LEFT JOIN public.teams t ON p.team_id = t.id
            LEFT JOIN public.areas a ON p.area_id = a.id
            WHERE p.id = daily_closings.user_id AND (
                (auth.uid() IN (SELECT id FROM public.profiles WHERE role = 'system_admin')) OR
                (t.manager_id = auth.uid()) OR
                (a.manager_id = auth.uid())
            )
        )
    );

-- RLS Policies for attendance_records table
-- Users can manage attendance for their own daily closings
CREATE POLICY "Users can manage own attendance" ON public.attendance_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.daily_closings
            WHERE id = attendance_records.daily_closing_id AND user_id = auth.uid()
        )
    );

-- Managers can view team attendance records
CREATE POLICY "Managers can view team attendance" ON public.attendance_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.daily_closings dc
            JOIN public.profiles p ON dc.user_id = p.id
            LEFT JOIN public.teams t ON p.team_id = t.id
            LEFT JOIN public.areas a ON p.area_id = a.id
            WHERE dc.id = attendance_records.daily_closing_id AND (
                (auth.uid() IN (SELECT id FROM public.profiles WHERE role = 'system_admin')) OR
                (t.manager_id = auth.uid()) OR
                (a.manager_id = auth.uid())
            )
        )
    );

-- RLS Policies for sales_records table
-- Users can manage sales for their own daily closings
CREATE POLICY "Users can manage own sales" ON public.sales_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.daily_closings
            WHERE id = sales_records.daily_closing_id AND user_id = auth.uid()
        )
    );

-- Managers can view team sales records
CREATE POLICY "Managers can view team sales" ON public.sales_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.daily_closings dc
            JOIN public.profiles p ON dc.user_id = p.id
            LEFT JOIN public.teams t ON p.team_id = t.id
            LEFT JOIN public.areas a ON p.area_id = a.id
            WHERE dc.id = sales_records.daily_closing_id AND (
                (auth.uid() IN (SELECT id FROM public.profiles WHERE role = 'system_admin')) OR
                (t.manager_id = auth.uid()) OR
                (a.manager_id = auth.uid())
            )
        )
    );

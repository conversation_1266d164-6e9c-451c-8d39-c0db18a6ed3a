'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/hooks/useAuth'

export default function AuthTestPage() {
  const { user, profile, loading } = useAuth()
  const [testResults, setTestResults] = useState<any[]>([])
  const supabase = createClient()

  useEffect(() => {
    runAuthTests()
  }, [user, profile])

  const runAuthTests = async () => {
    const results = []

    // Test 1: Check auth state
    results.push({
      test: 'Auth State',
      result: user ? 'PASS' : 'FAIL',
      details: user ? `User ID: ${user.id}, Email: ${user.email}` : 'No user found'
    })

    // Test 1.5: Check loading state
    results.push({
      test: 'Loading State',
      result: loading ? 'LOADING' : 'COMPLETE',
      details: loading ? 'Authentication still loading' : 'Authentication check complete'
    })

    // Test 2: Check profile
    results.push({
      test: 'Profile Data',
      result: profile ? 'PASS' : 'FAIL',
      details: profile ? `Role: ${profile.role}, Name: ${profile.full_name}` : 'No profile found'
    })

    if (user) {
      // Test 3: Check session
      try {
        const { data: session } = await supabase.auth.getSession()
        results.push({
          test: 'Session Check',
          result: session.session ? 'PASS' : 'FAIL',
          details: session.session ? 'Valid session found' : 'No valid session'
        })
      } catch (error: any) {
        results.push({
          test: 'Session Check',
          result: 'ERROR',
          details: error.message
        })
      }

      // Test 4: Check profile access
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()

        results.push({
          test: 'Profile Access',
          result: data ? 'PASS' : 'FAIL',
          details: data ? 'Can access profile' : `Error: ${error?.message}`
        })
      } catch (error: any) {
        results.push({
          test: 'Profile Access',
          result: 'ERROR',
          details: error.message
        })
      }

      // Test 5: Check daily closing access
      try {
        const today = new Date().toISOString().split('T')[0]
        const { data, error } = await supabase
          .from('daily_closings')
          .select('*')
          .eq('user_id', user.id)
          .eq('closing_date', today)

        results.push({
          test: 'Daily Closing Access',
          result: !error ? 'PASS' : 'FAIL',
          details: !error ? `Found ${data?.length || 0} records` : `Error: ${error.message}`
        })
      } catch (error: any) {
        results.push({
          test: 'Daily Closing Access',
          result: 'ERROR',
          details: error.message
        })
      }
    }

    setTestResults(results)
  }

  const signOut = async () => {
    await supabase.auth.signOut()
    window.location.href = '/login'
  }

  const testLogin = async () => {
    try {
      // Try to sign in with test credentials
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'password123' // You need to set this password in Supabase
      })

      if (error) {
        alert('Login failed: ' + error.message)
      } else {
        alert('Login successful! Refreshing page...')
        window.location.reload()
      }
    } catch (error: any) {
      alert('Login error: ' + error.message)
    }
  }

  return (
    <div className="container mx-auto p-8" dir="rtl">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">اختبار المصادقة</h1>
          <div className="space-x-2 space-x-reverse">
            <button
              onClick={runAuthTests}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              إعادة الاختبار
            </button>
            {!user && (
              <button
                onClick={testLogin}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                اختبار تسجيل الدخول
              </button>
            )}
            {user && (
              <button
                onClick={signOut}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                تسجيل الخروج
              </button>
            )}
          </div>
        </div>

        {loading && (
          <div className="text-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>جاري التحميل...</p>
          </div>
        )}

        <div className="grid gap-4">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">حالة المصادقة</h2>
            <div className="space-y-2">
              <p><strong>المستخدم:</strong> {user ? user.email : 'غير مسجل الدخول'}</p>
              <p><strong>الملف الشخصي:</strong> {profile ? profile.full_name || profile.email : 'غير متوفر'}</p>
              <p><strong>الدور:</strong> {profile?.role || 'غير محدد'}</p>
              <p><strong>حالة التحميل:</strong> {loading ? 'جاري التحميل' : 'مكتمل'}</p>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">نتائج الاختبارات</h2>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded">
                  <div>
                    <span className="font-medium">{result.test}</span>
                    <p className="text-sm text-gray-600">{result.details}</p>
                  </div>
                  <span className={`px-2 py-1 rounded text-sm font-medium ${
                    result.result === 'PASS' ? 'bg-green-100 text-green-800' :
                    result.result === 'FAIL' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {result.result}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">الإجراءات</h2>
            <div className="space-y-2">
              <a 
                href="/login" 
                className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 ml-2"
              >
                تسجيل الدخول
              </a>
              <a 
                href="/dashboard/daily-closing" 
                className="inline-block px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 ml-2"
              >
                التقفيل اليومي
              </a>
              <a 
                href="/dashboard" 
                className="inline-block px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                لوحة التحكم
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

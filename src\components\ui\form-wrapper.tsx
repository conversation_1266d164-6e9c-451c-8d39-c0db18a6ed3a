'use client'

import { useState, useCallback } from 'react'
import { useLoading } from '@/components/providers/LoadingProvider'
import { Button } from './button'
import { cn } from '@/lib/utils'
import { toast } from '@/hooks/use-toast'

interface FormWrapperProps {
  children: React.ReactNode
  onSubmit: (formData: FormData) => Promise<any>
  className?: string
  loadingText?: string
  successMessage?: string
  onSuccess?: (result: any) => void
  onError?: (error: any) => void
}

export function FormWrapper({
  children,
  onSubmit,
  className,
  loadingText = 'جاري الإرسال...',
  successMessage,
  onSuccess,
  onError
}: FormWrapperProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { withLoading } = useLoading()

  const handleSubmit = useCallback(async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (isSubmitting) return // Prevent double submission

    const formData = new FormData(e.currentTarget)
    setIsSubmitting(true)

    try {
      const result = await withLoading(
        onSubmit(formData),
        loadingText
      )

      if (successMessage) {
        toast.success(successMessage)
      }

      onSuccess?.(result)
    } catch (err: any) {
      const errorMessage = err.message || 'حدث خطأ أثناء الإرسال'
      toast.error(errorMessage)
      onError?.(err)
    } finally {
      setIsSubmitting(false)
    }
  }, [isSubmitting, onSubmit, loadingText, successMessage, onSuccess, onError, withLoading])

  return (
    <form onSubmit={handleSubmit} className={cn('space-y-4', className)}>
      {children}
    </form>
  )
}

interface SubmitButtonProps {
  children: React.ReactNode
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  className?: string
  disabled?: boolean
}

export function SubmitButton({
  children,
  variant = 'default',
  size = 'default',
  className,
  disabled
}: SubmitButtonProps) {
  return (
    <Button
      type="submit"
      variant={variant}
      size={size}
      className={className}
      disabled={disabled}
    >
      {children}
    </Button>
  )
}
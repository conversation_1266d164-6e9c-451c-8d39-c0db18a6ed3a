'use client'

import { useState } from 'react'
import { Loading, LoadingPage, QuickLoading, InlineLoading, CardLoader } from '@/components/ui/loading'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export function LoaderDemo() {
  const [showFullPage, setShowFullPage] = useState(false)
  const [currentVariant, setCurrentVariant] = useState<"spinner" | "dots" | "pulse" | "bars">("dots")

  const variants = [
    { key: "spinner" as const, name: "دوار", description: "الشكل التقليدي للتحميل" },
    { key: "dots" as const, name: "نقاط", description: "نقاط متحركة بسيطة" },
    { key: "pulse" as const, name: "نبضة", description: "دائرة نابضة" },
    { key: "bars" as const, name: "أعمدة", description: "أعمدة متحركة" }
  ]

  const sizes = [
    { key: "sm" as const, name: "صغير" },
    { key: "md" as const, name: "متوسط" },
    { key: "lg" as const, name: "كبير" },
    { key: "xl" as const, name: "كبير جداً" }
  ]

  if (showFullPage) {
    return (
      <div className="relative">
        <LoadingPage text="مثال على شاشة التحميل الكاملة" variant={currentVariant} />
        <Button
          onClick={() => setShowFullPage(false)}
          className="absolute top-4 right-4 z-50"
          variant="outline"
        >
          إغلاق
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-8 p-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">مكتبة أشكال التحميل</h1>
        <p className="text-muted-foreground">أشكال تحميل حديثة وبسيطة للتطبيق</p>
      </div>

      {/* Variant Selector */}
      <Card>
        <CardHeader>
          <CardTitle>اختيار نوع التحميل</CardTitle>
          <CardDescription>جرب الأنواع المختلفة من أشكال التحميل</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {variants.map((variant) => (
              <Button
                key={variant.key}
                variant={currentVariant === variant.key ? "default" : "outline"}
                onClick={() => setCurrentVariant(variant.key)}
                className="flex flex-col h-auto p-4"
              >
                <Loading variant={variant.key} size="sm" />
                <span className="mt-2 text-xs">{variant.name}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Size Examples */}
      <Card>
        <CardHeader>
          <CardTitle>الأحجام المختلفة</CardTitle>
          <CardDescription>نفس الشكل بأحجام مختلفة</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {sizes.map((size) => (
              <div key={size.key} className="text-center space-y-3">
                <Loading variant={currentVariant} size={size.key} />
                <Badge variant="outline">{size.name}</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Usage Examples */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Quick Loading */}
        <Card>
          <CardHeader>
            <CardTitle>تحميل سريع</CardTitle>
            <CardDescription>للاستخدام داخل المكونات</CardDescription>
          </CardHeader>
          <CardContent>
            <QuickLoading text="جاري التحميل..." variant={currentVariant} />
          </CardContent>
        </Card>

        {/* Inline Loading */}
        <Card>
          <CardHeader>
            <CardTitle>تحميل مضمن</CardTitle>
            <CardDescription>للاستخدام مع النصوص</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <InlineLoading text="جاري حفظ البيانات..." variant={currentVariant} />
              <InlineLoading text="جاري تحديث المعلومات..." variant={currentVariant} />
              <InlineLoading text="جاري الإرسال..." variant={currentVariant} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Card Loader */}
      <Card>
        <CardHeader>
          <CardTitle>تحميل البطاقات</CardTitle>
          <CardDescription>شكل تحميل للبطاقات مع تأثير اللمعان</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            <CardLoader />
            <CardLoader />
            <CardLoader />
          </div>
        </CardContent>
      </Card>

      {/* Full Page Demo */}
      <Card>
        <CardHeader>
          <CardTitle>شاشة التحميل الكاملة</CardTitle>
          <CardDescription>مثال على شاشة التحميل التي تغطي الصفحة كاملة</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setShowFullPage(true)} className="w-full">
            عرض شاشة التحميل الكاملة
          </Button>
        </CardContent>
      </Card>

      {/* Code Examples */}
      <Card>
        <CardHeader>
          <CardTitle>أمثلة الكود</CardTitle>
          <CardDescription>كيفية استخدام أشكال التحميل في الكود</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div className="bg-muted p-4 rounded-lg">
              <p className="font-semibold mb-2">تحميل بسيط:</p>
              <code className="text-primary">
                {`<Loading variant="${currentVariant}" size="md" />`}
              </code>
            </div>
            
            <div className="bg-muted p-4 rounded-lg">
              <p className="font-semibold mb-2">تحميل مع نص:</p>
              <code className="text-primary">
                {`<Loading variant="${currentVariant}" size="md" text="جاري التحميل..." showText />`}
              </code>
            </div>
            
            <div className="bg-muted p-4 rounded-lg">
              <p className="font-semibold mb-2">تحميل مضمن:</p>
              <code className="text-primary">
                {`<InlineLoading text="جاري الحفظ..." variant="${currentVariant}" />`}
              </code>
            </div>
            
            <div className="bg-muted p-4 rounded-lg">
              <p className="font-semibold mb-2">تحميل البطاقات:</p>
              <code className="text-primary">
                {`<CardLoader />`}
              </code>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
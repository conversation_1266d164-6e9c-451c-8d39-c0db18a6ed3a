import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  const startTime = Date.now()

  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  const pathname = request.nextUrl.pathname

  // Skip auth check for static files and API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/auth') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return supabaseResponse
  }

  try {
    // Get user with timeout and better error handling
    const userPromise = supabase.auth.getUser()
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Auth timeout')), 2000) // Reduced to 2 seconds
    )

    let user = null
    try {
      const result = await Promise.race([userPromise, timeoutPromise]) as any
      user = result.data?.user || null
    } catch (error) {
      console.warn('Auth check failed in middleware:', error)
      // Continue without user for non-critical paths
      if (pathname.startsWith('/dashboard')) {
        return NextResponse.redirect(new URL('/login', request.url))
      }
    }

    // Get user profile only when absolutely necessary
    let userProfile: { role: string; role_level: number } | null = null

    const needsProfile = (pathname === '/login' && user)

    if (user && needsProfile) {
      try {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role, role_level')
          .eq('id', user.id)
          .single()

        userProfile = profile
      } catch (error) {
        console.error('Profile fetch error:', error)
        // Continue without profile for non-critical paths
      }
    }



    // Protect dashboard routes
    if (pathname.startsWith('/dashboard')) {
      if (!user) {
        return NextResponse.redirect(new URL('/login', request.url))
      }
    }

    // Redirect authenticated users away from login page
    if (pathname === '/login' && user) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }

    // Log performance in development
    if (process.env.NODE_ENV === 'development') {
      const duration = Date.now() - startTime
      if (duration > 1000) {
        console.warn(`Slow middleware execution: ${duration}ms for ${pathname}`)
      }
    }

    return supabaseResponse
  } catch (error) {
    console.error('Middleware error:', error)

    // For critical paths, redirect to login on error
    if (pathname.startsWith('/dashboard')) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    return supabaseResponse
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api/auth (auth API routes)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|api/auth|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}

'use client'

import { useState } from 'react'
import { ProfileView } from '@/components/profile/ProfileView'
import { UserEditForm } from '@/components/admin/UserEditForm'

interface UserProfilePageProps {
  userId: string
}

export function UserProfilePage({ userId }: UserProfilePageProps) {
  const [isEditing, setIsEditing] = useState(false)

  const handleEditClick = () => {
    setIsEditing(true)
  }

  const handleEditComplete = () => {
    setIsEditing(false)
  }

  if (isEditing) {
    return (
      <UserEditForm
        userId={userId}
        onCancel={handleEditComplete}
        onSuccess={handleEditComplete}
        showBackButton={true}
      />
    )
  }

  return (
    <ProfileView
      userId={userId}
      showBackButton={true}
      showEditButton={true}
      onEditClick={handleEditClick}
    />
  )
}

'use client'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from '@/hooks/use-toast'

export function ToastTest() {
  const testSuccess = () => {
    toast.success('تم الحفظ بنجاح!', {
      description: 'تم حفظ البيانات في قاعدة البيانات'
    })
  }

  const testError = () => {
    toast.error('حدث خطأ في النظام', {
      description: 'يرجى المحاولة مرة أخرى'
    })
  }

  const testWarning = () => {
    toast.warning('تحذير: يرجى التحقق من البيانات', {
      description: 'هناك بعض البيانات المفقودة'
    })
  }

  const testInfo = () => {
    toast.info('معلومة مهمة', {
      description: 'يرجى قراءة هذه المعلومة بعناية'
    })
  }

  const testLoading = () => {
    const loadingToast = toast.loading('جاري التحميل...')
    
    setTimeout(() => {
      toast.dismiss(loadingToast)
      toast.success('تم التحميل بنجاح!')
    }, 3000)
  }

  const testPromise = () => {
    const promise = new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.5) {
          resolve('نجح!')
        } else {
          reject('فشل!')
        }
      }, 2000)
    })

    toast.promise(promise, {
      loading: 'جاري المعالجة...',
      success: 'تمت المعالجة بنجاح!',
      error: 'فشلت المعالجة'
    })
  }

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>اختبار نظام التنبيهات</CardTitle>
        <CardDescription>
          اختبر أنواع مختلفة من التنبيهات باستخدام Sonner
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <Button onClick={testSuccess} className="w-full" variant="default">
          تنبيه نجاح
        </Button>
        <Button onClick={testError} className="w-full" variant="destructive">
          تنبيه خطأ
        </Button>
        <Button onClick={testWarning} className="w-full" variant="outline">
          تنبيه تحذير
        </Button>
        <Button onClick={testInfo} className="w-full" variant="secondary">
          تنبيه معلومات
        </Button>
        <Button onClick={testLoading} className="w-full" variant="outline">
          تنبيه تحميل
        </Button>
        <Button onClick={testPromise} className="w-full" variant="ghost">
          تنبيه وعد (Promise)
        </Button>
      </CardContent>
    </Card>
  )
}

import { requireAuth, getUserProfile } from '@/lib/auth'

export default async function ProfileTestPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Profile Test Page</h1>
      
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">User Information</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>ID:</strong> {user.id}
            </div>
            <div>
              <strong>Email:</strong> {user.email}
            </div>
            <div>
              <strong>Created:</strong> {new Date(user.created_at).toLocaleString()}
            </div>
            <div>
              <strong>Metadata:</strong> {JSON.stringify(user.user_metadata)}
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Profile Information</h2>
          {profile ? (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Full Name:</strong> {profile.full_name || 'Not set'}
              </div>
              <div>
                <strong>Phone:</strong> {profile.phone || 'Not set'}
              </div>
              <div>
                <strong>Role:</strong> {profile.role}
              </div>
              <div>
                <strong>Role Level:</strong> {profile.role_level}
              </div>
              <div>
                <strong>Area ID:</strong> {profile.area_id || 'Not set'}
              </div>
              <div>
                <strong>Team ID:</strong> {profile.team_id || 'Not set'}
              </div>
            </div>
          ) : (
            <div className="text-red-600 font-semibold">
              No profile found in database
            </div>
          )}
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <p className="text-blue-800">
            If you can see this page, authentication is working correctly.
            The issue with the main profile page might be related to the DashboardLayout or ProfileManagement component.
          </p>
        </div>

        <div className="space-y-2">
          <a 
            href="/dashboard/profile" 
            className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Try Main Profile Page
          </a>
          <br />
          <a 
            href="/dashboard" 
            className="inline-block bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>
  )
}

'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useNavigation } from '@/components/providers/NavigationProvider'
import { cn } from '@/lib/utils'

interface NavigationLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  loadingText?: string
  onClick?: () => void
}

export function NavigationLink({ 
  href, 
  children, 
  className, 
  loadingText,
  onClick 
}: NavigationLinkProps) {
  const pathname = usePathname()
  const { navigateWithLoading } = useNavigation()

  const handleClick = (e: React.MouseEvent) => {
    // Don't navigate if it's the same page
    if (href === pathname) {
      e.preventDefault()
      return
    }

    e.preventDefault()
    onClick?.()
    navigateWithLoading(href, loadingText)
  }

  return (
    <Link 
      href={href} 
      className={className}
      onClick={handleClick}
    >
      {children}
    </Link>
  )
}

// Enhanced version for sidebar menu items
export function SidebarNavigationLink({ 
  href, 
  children, 
  className, 
  isActive,
  loadingText 
}: NavigationLinkProps & { isActive?: boolean }) {
  const pathname = usePathname()
  const { navigateWithLoading } = useNavigation()

  const handleClick = (e: React.MouseEvent) => {
    if (href === pathname) {
      e.preventDefault()
      return
    }

    e.preventDefault()
    navigateWithLoading(href, loadingText || 'جاري تحميل الصفحة...')
  }

  return (
    <Link 
      href={href} 
      className={cn(
        "flex items-center gap-2 transition-all duration-200 hover:bg-sidebar-accent/50",
        isActive && "bg-sidebar-accent text-sidebar-accent-foreground",
        className
      )}
      onClick={handleClick}
    >
      {children}
    </Link>
  )
}
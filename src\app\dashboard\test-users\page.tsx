'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'

export default function TestUsersPage() {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const testSimpleAPI = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      console.log('Testing simple API...')
      const response = await fetch('/api/admin/users/simple')
      const data = await response.json()

      console.log('Response:', response.status, data)

      if (!response.ok) {
        throw new Error(data.error || 'خطأ في الاستجابة')
      }

      setUsers(data.users || [])
      setSuccess(`تم جلب ${data.users?.length || 0} مستخدم بنجاح`)
    } catch (err: any) {
      console.error('Error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const testMainAPI = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      console.log('Testing main API...')
      const response = await fetch('/api/admin/users')
      const data = await response.json()

      console.log('Response:', response.status, data)

      if (!response.ok) {
        throw new Error(data.error || 'خطأ في الاستجابة')
      }

      setUsers(data.users || [])
      setSuccess(`تم جلب ${data.users?.length || 0} مستخدم بنجاح من API الرئيسي`)
    } catch (err: any) {
      console.error('Error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8 space-y-6" dir="rtl">
      <div>
        <h1 className="text-3xl font-bold">اختبار API المستخدمين</h1>
        <p className="text-muted-foreground">اختبار جلب بيانات المستخدمين من APIs مختلفة</p>
      </div>

      <div className="flex gap-4">
        <Button onClick={testSimpleAPI} disabled={loading} className="cursor-pointer">
          {loading ? 'جاري الاختبار...' : 'اختبار API البسيط'}
        </Button>
        <Button onClick={testMainAPI} disabled={loading} className="cursor-pointer">
          {loading ? 'جاري الاختبار...' : 'اختبار API الرئيسي'}
        </Button>
      </div>

      {success && (
        <Alert className="border-green-200 bg-green-50 text-green-800">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {users.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>المستخدمين المجلوبين ({users.length})</CardTitle>
            <CardDescription>قائمة المستخدمين من API</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {users.map((user: any) => (
                <div key={user.id} className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{user.full_name || user.email}</p>
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm">{user.role}</p>
                      {user.area_id && <p className="text-xs text-muted-foreground">منطقة: {user.area_id}</p>}
                      {user.team_id && <p className="text-xs text-muted-foreground">فريق: {user.team_id}</p>}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>معلومات التشخيص</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>متغيرات البيئة:</strong></p>
            <p>NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ موجود' : '❌ مفقود'}</p>
            <p><strong>الوقت الحالي:</strong> {new Date().toLocaleString('ar-SA')}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

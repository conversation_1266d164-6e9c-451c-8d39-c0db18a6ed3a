# دليل نظام البحث الشامل - سحابة المدينة

## نظرة عامة

تم تطوير نظام بحث شامل ومتقدم لتطبيق "سحابة المدينة" يدعم البحث عبر جميع ميزات النظام مع احترام الصلاحيات والأدوار المختلفة.

## المميزات الرئيسية

### 🔍 البحث الذكي
- **البحث الفوري**: نتائج فورية مع تأخير 300ms لتحسين الأداء
- **البحث متعدد الفئات**: بحث عبر المستخدمين، المناطق، الفرق، الطلبات، والمزيد
- **البحث الضبابي**: يدعم البحث باللغة العربية مع تطابق جزئي
- **التنقل بلوحة المفاتيح**: دعم كامل للتنقل بالأسهم و Enter و Escape

### 🔐 البحث المبني على الصلاحيات
- **مدير النظام**: بحث شامل في جميع البيانات والميزات
- **مدير المنطقة**: بحث في بيانات المنطقة المخصصة له
- **مدير الفريق**: بحث في بيانات الفريق المخصص له
- **موظف المبيعات**: بحث في بياناته الشخصية فقط

### 📱 تصميم متجاوب
- **سطح المكتب**: شريط بحث مع نتائج منبثقة
- **الهاتف المحمول**: زر بحث مع نافذة منبثقة كاملة
- **دعم RTL**: تصميم كامل يدعم اللغة العربية

## الفئات المدعومة

### 1. التنقل (Navigation)
- البحث في عناصر القائمة الجانبية
- روابط سريعة للصفحات المختلفة
- مخصص حسب دور المستخدم

### 2. المستخدمين (Users)
- البحث بالاسم أو البريد الإلكتروني
- عرض الدور والمنطقة والفريق
- مقيد حسب صلاحيات المستخدم

### 3. المناطق (Areas)
- البحث بالاسم أو الوصف
- عرض مدير المنطقة
- متاح لمدير النظام فقط

### 4. الفرق (Teams)
- البحث بالاسم أو الوصف
- عرض مدير الفريق والمنطقة
- متاح لمدير النظام ومدير المنطقة

### 5. الطلبات (Tickets)
- البحث بالعنوان أو الوصف
- عرض الحالة والأولوية
- مقيد حسب الهيكل الهرمي

### 6. التقفيل اليومي (Daily Closings)
- البحث بالتاريخ
- عرض حالة الإكمال والمبيعات
- متاح لموظفي المبيعات فقط

### 7. الباقات (Packages)
- البحث بالاسم أو الوصف
- عرض السعر والحالة
- متاح لمدير النظام فقط

## الاستخدام

### البحث الأساسي
```typescript
// استخدام hook البحث
const {
  query,
  setQuery,
  results,
  categories,
  loading,
  error,
  isOpen,
  setIsOpen
} = useSearch()
```

### البحث المخصص
```typescript
// بحث مخصص بفلاتر
const searchOptions = {
  debounceMs: 500,
  minQueryLength: 3,
  limit: 10,
  type: 'user' // أو 'ticket', 'area', إلخ
}

const search = useSearch(searchOptions)
```

### اختصارات لوحة المفاتيح
- **Ctrl+K** أو **Cmd+K**: تركيز على شريط البحث
- **↑/↓**: التنقل بين النتائج
- **Enter**: فتح النتيجة المحددة
- **Escape**: إغلاق نتائج البحث

## التطبيق

### 1. شريط البحث
```tsx
import { SearchBar } from '@/components/search/SearchBar'

<SearchBar 
  placeholder="البحث في النظام..."
  showMobileButton={true}
/>
```

### 2. نتائج البحث
```tsx
import { SearchResults } from '@/components/search/SearchResults'

<SearchResults
  categories={categories}
  results={results}
  loading={loading}
  error={error}
  query={query}
  selectedIndex={selectedIndex}
  onResultClick={handleResultClick}
/>
```

## API البحث

### نقطة النهاية
```
GET /api/search?q={query}&type={type}&limit={limit}
```

### المعاملات
- **q**: نص البحث (مطلوب، الحد الأدنى 2 أحرف)
- **type**: نوع البحث (اختياري، افتراضي: 'all')
- **limit**: عدد النتائج (اختياري، افتراضي: 20)

### استجابة API
```json
{
  "results": [...],
  "categories": {
    "users": {
      "name": "users",
      "arabicName": "المستخدمين",
      "results": [...],
      "icon": "Users"
    }
  },
  "total": 15,
  "query": "محمد"
}
```

## الأمان والصلاحيات

### Row Level Security (RLS)
- جميع استعلامات البحث تحترم سياسات RLS
- التحقق من الصلاحيات على مستوى API
- فلترة النتائج حسب الدور والهيكل الهرمي

### التحقق من الصلاحيات
```typescript
// مثال على فلترة النتائج حسب الدور
if (profile.role === 'area_manager' && profile.area_id) {
  userQuery = userQuery.eq('area_id', profile.area_id)
} else if (profile.role === 'team_manager' && profile.team_id) {
  userQuery = userQuery.eq('team_id', profile.team_id)
}
```

## الأداء والتحسين

### تحسينات الأداء
- **Debouncing**: تأخير 300ms لتقليل الطلبات
- **إلغاء الطلبات**: إلغاء الطلبات السابقة عند البحث الجديد
- **تحديد النتائج**: حد أقصى 20 نتيجة افتراضياً
- **فهرسة قاعدة البيانات**: فهارس على الحقول المبحوث فيها

### ذاكرة التخزين المؤقت
- تخزين مؤقت للنتائج الحديثة
- تخزين مؤقت لعناصر التنقل
- تحديث تلقائي عند تغيير البيانات

## استكشاف الأخطاء

### مشاكل شائعة

#### البحث لا يعمل
```bash
# تحقق من وجود المكونات المطلوبة
npx shadcn@latest add popover scroll-area
```

#### نتائج فارغة
- تحقق من صلاحيات المستخدم
- تحقق من سياسات RLS في قاعدة البيانات
- تحقق من صحة استعلامات البحث

#### أخطاء TypeScript
```bash
# تحقق من أنواع البيانات
npm run type-check
```

## التطوير المستقبلي

### ميزات مخططة
- **البحث المتقدم**: فلاتر متقدمة وخيارات ترتيب
- **البحث الصوتي**: دعم البحث بالصوت
- **التاريخ**: حفظ تاريخ البحث
- **الاقتراحات**: اقتراحات ذكية للبحث
- **التحليلات**: إحصائيات استخدام البحث

### تحسينات الأداء
- **البحث المفهرس**: استخدام Elasticsearch أو مشابه
- **البحث المخزن مؤقتاً**: تخزين مؤقت متقدم
- **البحث غير المتزامن**: معالجة غير متزامنة للبحث

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من هذا الدليل أولاً
2. راجع سجلات وحدة التحكم للأخطاء
3. تحقق من حالة قاعدة البيانات والاتصال
4. اتصل بفريق التطوير للمساعدة المتقدمة

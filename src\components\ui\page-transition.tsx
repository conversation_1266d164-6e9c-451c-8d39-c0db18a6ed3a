'use client'

import { useEffect, useState, useRef } from 'react'
import { usePathname } from 'next/navigation'
import { QuickLoading } from './loading'
import { cn } from '@/lib/utils'

interface PageTransitionProps {
  children: React.ReactNode
  className?: string
  duration?: number
}

export function PageTransition({ children, className, duration = 200 }: PageTransitionProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const pathname = usePathname()
  const previousPathname = useRef(pathname)

  useEffect(() => {
    // Only show loading if pathname actually changed
    if (pathname !== previousPathname.current) {
      setIsVisible(false)
      setIsLoading(true)

      // Shorter duration for faster transitions
      const timer = setTimeout(() => {
        setIsLoading(false)
        setIsVisible(true)
        previousPathname.current = pathname
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [pathname, duration])

  if (isLoading) {
    return (
      <div className="min-h-[200px] flex items-center justify-center">
        <QuickLoading text="جاري تحميل الصفحة..." />
      </div>
    )
  }

  return (
    <div
      className={cn(
        "transition-all duration-200 ease-in-out",
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-1",
        className
      )}
    >
      {children}
    </div>
  )
}

// Enhanced transition for navigation items
export function NavigationTransition({ children, isActive }: { children: React.ReactNode; isActive: boolean }) {
  return (
    <div
      className={cn(
        "transition-all duration-200 ease-in-out",
        isActive ? "bg-sidebar-accent text-sidebar-accent-foreground" : "hover:bg-sidebar-accent/50"
      )}
    >
      {children}
    </div>
  )
}

// Smooth fade transition for content changes
export function FadeTransition({
  children,
  show = true,
  duration = 200
}: {
  children: React.ReactNode
  show?: boolean
  duration?: number
}) {
  return (
    <div
      className={cn(
        "transition-all ease-in-out",
        show ? "opacity-100 scale-100" : "opacity-0 scale-95"
      )}
      style={{ transitionDuration: `${duration}ms` }}
    >
      {children}
    </div>
  )
}

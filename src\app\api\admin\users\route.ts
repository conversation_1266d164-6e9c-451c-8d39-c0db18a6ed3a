import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { isSystemAdmin } from '@/lib/roles'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Check if required environment variables are present
if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Missing required environment variables for Supabase admin client')
}

export async function GET() {
  try {
    // Check authentication using the same pattern as other APIs
    const cookieStore = await cookies()

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // The `setAll` method was called from a Server Component.
            }
          },
        },
      }
    )

    // Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || !isSystemAdmin(profile.role)) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول لهذه الصفحة' },
        { status: 403 }
      )
    }

    // Try to get all users with area and team information
    let users, error

    // First try with service role client
    if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
      const result = await supabaseAdmin
        .from('profiles')
        .select(`
          id,
          email,
          full_name,
          phone,
          role,
          role_level,
          area_id,
          team_id,
          created_at,
          updated_at,
          area:areas!profiles_area_id_fkey(id, name),
          team:teams!profiles_team_id_fkey(id, name)
        `)
        .order('created_at', { ascending: false })

      users = result.data
      error = result.error
    }

    // If service role fails, try with regular client (without relationships for now)
    if (error || !users) {
      console.warn('Service role failed, trying with regular client:', error?.message)

      const result = await supabase
        .from('profiles')
        .select(`
          id,
          email,
          full_name,
          phone,
          role,
          role_level,
          area_id,
          team_id,
          created_at,
          updated_at
        `)
        .order('created_at', { ascending: false })

      users = result.data
      error = result.error
    }

    if (error) {
      console.error('Error fetching users:', error)
      return NextResponse.json(
        { error: `خطأ في جلب المستخدمين: ${error.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json({ users })

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { UserRole } from '@/lib/supabase'
import { getRoleArabicName, getAssignableRoles } from '@/lib/roles'
import { useAuth } from '@/hooks/useAuth'
import { toast } from '@/hooks/use-toast'

interface AddUserFormProps {
  onUserAdded?: () => void
}

export function AddUserForm({ onUserAdded }: AddUserFormProps) {
  const { profile: currentUserProfile } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    full_name: '',
    phone: '',
    role: 'sales_employee' as UserRole
  })

  // Get roles that current user can assign
  const assignableRoles = currentUserProfile ? getAssignableRoles(currentUserProfile.role) : []

  const handleAddUser = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      // Call API route to create user (since we need service role key)
      const response = await fetch('/api/admin/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newUser),
      })

      const result = await response.json()

      if (!response.ok) {
        toast.error(result.error || 'حدث خطأ في إضافة المستخدم')
        return
      }

      toast.success('تم إضافة المستخدم بنجاح! سيتم تحويلك إلى قائمة المستخدمين...')

      // Reset form
      setNewUser({ email: '', password: '', full_name: '', phone: '', role: 'sales_employee' })

      // Notify parent component if callback provided
      if (onUserAdded) {
        onUserAdded()
      }

      // Navigate to users list after a short delay
      setTimeout(() => {
        router.push('/admin/users')
      }, 1500)
      
    } catch (err) {
      toast.error('حدث خطأ في الاتصال')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleAddUser()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-right">إضافة مستخدم جديد</CardTitle>
        <CardDescription className="text-right">
          أدخل بيانات المستخدم الجديد لإضافته إلى النظام
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4" dir="rtl">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-right block">البريد الإلكتروني</Label>
            <Input
              id="email"
              type="email"
              placeholder="أدخل البريد الإلكتروني"
              value={newUser.email}
              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
              required
              className="text-right"
              dir="rtl"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-right block">كلمة المرور</Label>
            <Input
              id="password"
              type="password"
              placeholder="أدخل كلمة المرور"
              value={newUser.password}
              onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
              required
              className="text-right"
              dir="rtl"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="full_name" className="text-right block">الاسم الكامل</Label>
            <Input
              id="full_name"
              type="text"
              placeholder="أدخل الاسم الكامل"
              value={newUser.full_name}
              onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
              className="text-right"
              dir="rtl"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone" className="text-right block">رقم الهاتف</Label>
            <Input
              id="phone"
              type="tel"
              placeholder="أدخل رقم الهاتف"
              value={newUser.phone}
              onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
              className="text-right"
              dir="rtl"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="role" className="text-right block">الدور</Label>
            <Select value={newUser.role} onValueChange={(value: UserRole) => setNewUser({ ...newUser, role: value })} dir="rtl">
              <SelectTrigger className="text-right" dir="rtl">
                <SelectValue placeholder="اختر الدور" />
              </SelectTrigger>
              <SelectContent className="text-right" dir="rtl">
                {assignableRoles.map((role) => (
                  <SelectItem key={role} value={role} className="text-right">
                    {getRoleArabicName(role)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-start gap-2 mt-6" dir="rtl">
            <Button type="submit" disabled={loading}>
              {loading ? 'جاري الإضافة...' : 'إضافة المستخدم'}
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => {
                setNewUser({ email: '', password: '', full_name: '', phone: '', role: 'sales_employee' })
                setError('')
                setSuccess('')
              }}
            >
              مسح النموذج
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

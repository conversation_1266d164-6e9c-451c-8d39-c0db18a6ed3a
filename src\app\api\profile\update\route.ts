import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    )

    // Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { full_name, phone } = body

    // Validate required fields
    if (!full_name || !full_name.trim()) {
      return NextResponse.json(
        { error: 'الاسم الكامل مطلوب' },
        { status: 400 }
      )
    }

    // Upsert profile (update if exists, create if doesn't)
    const { data, error } = await supabase
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email || '',
        full_name: full_name.trim(),
        phone: phone?.trim() || null,
        role: 'sales_employee', // Default role for new profiles
        role_level: 4, // Default role level
        updated_at: new Date().toISOString()
      })
      .select(`
        *,
        team:teams!profiles_team_id_fkey(name, area:areas!teams_area_id_fkey(name)),
        area:areas!profiles_area_id_fkey(name)
      `)
      .single()

    if (error) {
      console.error('Error updating/creating profile:', error)
      return NextResponse.json(
        { error: 'حدث خطأ أثناء تحديث الملف الشخصي' },
        { status: 500 }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Profile update error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

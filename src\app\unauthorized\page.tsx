import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ShieldX } from 'lucide-react'
import Link from 'next/link'

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="space-y-4">
          <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center">
            <ShieldX className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl font-bold">
            غير مصرح لك بالوصول
          </CardTitle>
          <CardDescription>
            ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <Button asChild className="w-full">
            <Link href="/">
              العودة إلى الصفحة الرئيسية
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

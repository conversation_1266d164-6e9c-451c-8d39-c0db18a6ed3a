'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import type { Database, UserRole, RoleLevel } from '@/lib/supabase'
import { isSystemAdmin, isAreaManagerOr<PERSON>igher, isTeamManagerOrHigher, hasHigherOrEqualRole } from '@/lib/roles'
import { useSupabaseConnection } from '@/hooks/useSupabaseConnection'

type Profile = Database['public']['Tables']['profiles']['Row']

interface AuthContextType {
  user: User | null
  profile: Profile | null
  loading: boolean
  loggingOut: boolean
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signOut: () => Promise<{ error: any }>
  isAdmin: boolean
  isSystemAdmin: boolean
  isAreaManagerOrHigher: boolean
  isTeamManagerOrHigher: boolean
  isAuthenticated: boolean
  hasRole: (role: UserRole) => boolean
  hasRoleLevel: (level: RoleLevel) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [profileLoading, setProfileLoading] = useState(false)
  const [loggingOut, setLoggingOut] = useState(false)
  const [initialized, setInitialized] = useState(false)
  const supabase = createClient()
  
  // Use connection recovery hook
  useSupabaseConnection()

  // Profile fetch controller for cancelling requests
  let profileFetchController: AbortController | null = null

  useEffect(() => {
    let mounted = true

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (mounted) {
        console.warn('Auth loading timeout - setting loading to false')
        setLoading(false)
      }
    }, 5000) // Reduced to 5 second timeout

    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('Getting initial session...')
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Error getting session:', error)
        }

        if (!mounted) return

        console.log('Initial session:', session?.user ? 'User found' : 'No user')
        setUser(session?.user ?? null)

        if (session?.user) {
          console.log('Fetching profile for initial session user:', session.user.id)
          await fetchProfile(session.user.id)
        } else {
          console.log('No user in session, setting loading to false')
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        if (mounted) {
          clearTimeout(loadingTimeout)
          setLoading(false)
          console.log('Initial session loading completed')
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return

        console.log('Auth state change:', event, session?.user ? 'User present' : 'No user')

        // Update user state immediately
        const newUser = session?.user ?? null
        setUser(newUser)

        if (newUser && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
          console.log('User signed in or token refreshed, fetching profile')
          profileFetchController?.abort() // Cancel any ongoing profile fetch
          profileFetchController = new AbortController()
          await fetchProfile(newUser.id, profileFetchController.signal)
        } else if (!newUser || event === 'SIGNED_OUT') {
          console.log('User signed out or no user, clearing profile')
          profileFetchController?.abort()
          setProfile(null)
        }

        if (mounted) {
          setLoading(false)
          setInitialized(true)
          console.log('Auth state change loading completed')
        }
      }
    )

    return () => {
      mounted = false
      clearTimeout(loadingTimeout)
      profileFetchController?.abort()
      subscription.unsubscribe()
    }
  }, [])

  const fetchProfile = async (userId: string, signal?: AbortSignal) => {
    // Don't fetch if already loading or if we already have the profile for this user
    if (profileLoading || (profile && profile.id === userId)) {
      return
    }

    setProfileLoading(true)

    // Add a small delay to ensure auth context is established
    await new Promise(resolve => setTimeout(resolve, 100))

    try {
      console.log('Fetching profile for user ID:', userId)

      // Check if request was aborted
      if (signal?.aborted) {
        console.log('Profile fetch aborted')
        return
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      // Check again if request was aborted after fetch
      if (signal?.aborted) {
        console.log('Profile fetch aborted after request')
        return
      }

      if (data) {
        console.log('Profile fetched successfully:', data)
        setProfile(data)
      } else if (error) {
        console.log('Profile fetch error, using fallback:', error)
        // Always set a fallback profile to keep the app working
        const { data: { user } } = await supabase.auth.getUser()

        if (signal?.aborted) return

        const fallbackProfile = {
          id: userId,
          email: user?.email || '',
          full_name: user?.user_metadata?.full_name || null,
          phone: user?.user_metadata?.phone || null,
          role: 'sales_employee' as const, // Default to sales_employee for daily closing access
          role_level: 4 as const,
          area_id: null,
          team_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        setProfile(fallbackProfile)
        console.log('Fallback profile set:', fallbackProfile)
      }
    } catch (error) {
      console.log('Unexpected error, using fallback profile:', error)
      // Always set a fallback profile
      const fallbackProfile = {
        id: userId,
        email: '',
        full_name: null,
        phone: null,
        role: 'sales_employee' as const, // Default to sales_employee for daily closing access
        role_level: 4 as const,
        area_id: null,
        team_id: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      setProfile(fallbackProfile)
      console.log('Exception fallback profile set:', fallbackProfile)
    } finally {
      setProfileLoading(false)
      console.log('Profile loading completed')
    }
  }

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    
    return { data, error }
  }

  const signOut = async () => {
    try {
      console.log('Starting sign out process...')

      setLoggingOut(true)

      // Clear local state immediately to prevent UI issues
      setUser(null)
      setProfile(null)
      setLoading(false) // Ensure loading is false

      // Clear localStorage/sessionStorage if any
      try {
        localStorage.clear()
        sessionStorage.clear()
      } catch (e) {
        console.warn('Could not clear storage:', e)
      }

      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('Error signing out (continuing with redirect):', error)
        // Don't return error - continue with redirect
      } else {
        console.log('Successfully signed out from Supabase')
      }

      // Force redirect to login page regardless of Supabase result
      console.log('Redirecting to login page...')

      // Use multiple redirect methods for maximum reliability
      setTimeout(() => {
        window.location.href = '/login'
      }, 100)

      window.location.replace('/login')

      return { error: null }
    } catch (err) {
      console.error('Unexpected error during signout (continuing with redirect):', err)

      // Clear state and redirect even on unexpected errors
      setUser(null)
      setProfile(null)
      setLoading(false)

      // Force redirect even on errors
      setTimeout(() => {
        window.location.href = '/login'
      }, 100)

      window.location.replace('/login')

      return { error: null }
    }
  }

  const hasRole = (role: UserRole): boolean => {
    if (!profile) return false
    return hasHigherOrEqualRole(profile.role, role)
  }

  const hasRoleLevel = (level: RoleLevel): boolean => {
    if (!profile) return false
    return profile.role_level <= level
  }

  const value = {
    user,
    profile,
    loading,
    loggingOut,
    signIn,
    signOut,
    isAdmin: profile ? isSystemAdmin(profile.role) : false,
    isSystemAdmin: profile ? isSystemAdmin(profile.role) : false,
    isAreaManagerOrHigher: profile ? isAreaManagerOrHigher(profile.role) : false,
    isTeamManagerOrHigher: profile ? isTeamManagerOrHigher(profile.role) : false,
    isAuthenticated: !!user,
    hasRole,
    hasRoleLevel,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

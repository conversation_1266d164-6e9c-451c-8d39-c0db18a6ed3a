'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Users, User, MapPin } from 'lucide-react'
import { UserRole } from '@/lib/supabase'
import { getRoleArabicName } from '@/lib/roles'

type Team = {
  id: string
  name: string
  description: string | null
  area_id: string
  manager_id: string | null
  created_at: string
  updated_at: string
  area?: {
    id: string
    name: string
  } | null
  manager?: {
    id: string
    full_name: string
    email: string
  } | null
  members?: {
    id: string
    full_name: string
    email: string
    role: UserRole
  }[]
}

type Area = {
  id: string
  name: string
}

type Profile = {
  id: string
  full_name: string | null
  email: string
  role: UserRole
}

export function TeamsManagement() {
  const [teams, setTeams] = useState<Team[]>([])
  const [areas, setAreas] = useState<Area[]>([])
  const [availableManagers, setAvailableManagers] = useState<Profile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  // Filter states
  const [selectedAreaFilter, setSelectedAreaFilter] = useState('')
  
  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingTeam, setEditingTeam] = useState<Team | null>(null)
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    area_id: '',
    manager_id: ''
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    fetchTeams()
    fetchAreas()
    fetchAvailableManagers()
  }, [])

  const fetchTeams = async (areaId?: string) => {
    try {
      const url = areaId ? `/api/admin/teams?area_id=${areaId}` : '/api/admin/teams'
      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في جلب الفرق')
      }

      setTeams(data.teams || [])
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const fetchAreas = async () => {
    try {
      const response = await fetch('/api/admin/areas')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في جلب المناطق')
      }

      setAreas(data.areas || [])
    } catch (err: any) {
      console.error('Error fetching areas:', err)
    }
  }

  const fetchAvailableManagers = async () => {
    try {
      const response = await fetch('/api/admin/users')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في جلب المستخدمين')
      }

      // Filter users who can be team managers
      const managers = data.users?.filter((user: Profile) => 
        ['system_admin', 'area_manager', 'team_manager'].includes(user.role)
      ) || []

      setAvailableManagers(managers)
    } catch (err: any) {
      console.error('Error fetching managers:', err)
    }
  }

  const handleAreaFilterChange = (areaId: string) => {
    setSelectedAreaFilter(areaId)
    setLoading(true)
    fetchTeams(areaId || undefined)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      setError('اسم الفريق مطلوب')
      return
    }

    if (!formData.area_id) {
      setError('المنطقة مطلوبة')
      return
    }

    setFormLoading(true)
    setError('')

    try {
      const url = editingTeam ? `/api/admin/teams/${editingTeam.id}` : '/api/admin/teams'
      const method = editingTeam ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          area_id: formData.area_id,
          manager_id: formData.manager_id || null
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في حفظ الفريق')
      }

      setSuccess(editingTeam ? 'تم تحديث الفريق بنجاح' : 'تم إنشاء الفريق بنجاح')
      
      // Reset form and close dialog
      setFormData({ name: '', description: '', area_id: '', manager_id: '' })
      setIsAddDialogOpen(false)
      setIsEditDialogOpen(false)
      setEditingTeam(null)
      
      // Refresh teams list
      fetchTeams(selectedAreaFilter || undefined)
      
    } catch (err: any) {
      setError(err.message)
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (team: Team) => {
    setEditingTeam(team)
    setFormData({
      name: team.name,
      description: team.description || '',
      area_id: team.area_id,
      manager_id: team.manager_id || ''
    })
    setIsEditDialogOpen(true)
  }

  const handleDelete = async (team: Team) => {
    if (!confirm(`هل أنت متأكد من حذف الفريق "${team.name}"؟`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/teams/${team.id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في حذف الفريق')
      }

      setSuccess('تم حذف الفريق بنجاح')
      fetchTeams(selectedAreaFilter || undefined)
      
    } catch (err: any) {
      setError(err.message)
    }
  }

  const resetForm = () => {
    setFormData({ name: '', description: '', area_id: '', manager_id: '' })
    setEditingTeam(null)
    setError('')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">جاري تحميل الفرق...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">إدارة الفرق</h2>
          <p className="text-muted-foreground">إنشاء وإدارة فرق العمل</p>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
          setIsAddDialogOpen(open)
          if (!open) resetForm()
        }}>
          <DialogTrigger asChild>
            <Button className="cursor-pointer">
              <Plus className="h-4 w-4 ml-2" />
              إضافة فريق
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px] text-right" dir="rtl">
            <DialogHeader className="text-right">
              <DialogTitle>إضافة فريق جديد</DialogTitle>
              <DialogDescription>
                أدخل بيانات الفريق الجديد
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="name">اسم الفريق *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="أدخل اسم الفريق"
                  className="text-right"
                  dir="rtl"
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="area">المنطقة *</Label>
                <Select value={formData.area_id} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, area_id: value }))
                }>
                  <SelectTrigger className="text-right" dir="rtl">
                    <SelectValue placeholder="اختر المنطقة" />
                  </SelectTrigger>
                  <SelectContent>
                    {areas.map((area) => (
                      <SelectItem key={area.id} value={area.id}>
                        {area.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="أدخل وصف الفريق"
                  className="text-right"
                  dir="rtl"
                  rows={3}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="manager">مدير الفريق</Label>
                <Select value={formData.manager_id || "none"} onValueChange={(value) =>
                  setFormData(prev => ({ ...prev, manager_id: value === "none" ? "" : value }))
                }>
                  <SelectTrigger className="text-right" dir="rtl">
                    <SelectValue placeholder="اختر مدير الفريق" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">بدون مدير</SelectItem>
                    {availableManagers.map((manager) => (
                      <SelectItem key={manager.id} value={manager.id}>
                        <div className="flex items-center gap-2 text-right">
                          <span>{manager.full_name || manager.email}</span>
                          <Badge variant="outline" className="text-xs">
                            {getRoleArabicName(manager.role)}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex gap-2 justify-start">
                <Button type="submit" disabled={formLoading} className="cursor-pointer">
                  {formLoading ? 'جاري الحفظ...' : 'حفظ'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsAddDialogOpen(false)}
                  className="cursor-pointer"
                >
                  إلغاء
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">تصفية الفرق</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Label htmlFor="area-filter">تصفية حسب المنطقة</Label>
              <Select value={selectedAreaFilter || "all"} onValueChange={(value) => handleAreaFilterChange(value === "all" ? "" : value)}>
                <SelectTrigger className="text-right" dir="rtl">
                  <SelectValue placeholder="جميع المناطق" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المناطق</SelectItem>
                  {areas.map((area) => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Success/Error Messages */}
      {success && (
        <Alert className="border-green-200 bg-green-50 text-green-800">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && !isAddDialogOpen && !isEditDialogOpen && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Teams Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {teams.map((team) => (
          <Card key={team.id} className="text-right">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(team)}
                    className="cursor-pointer"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(team)}
                    className="cursor-pointer text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                <Users className="h-5 w-5 text-muted-foreground" />
              </div>
              <CardTitle className="text-lg">{team.name}</CardTitle>
              {team.description && (
                <CardDescription className="text-sm">
                  {team.description}
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {/* Area */}
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4" />
                  <span>
                    المنطقة: {team.area?.name || 'غير محدد'}
                  </span>
                </div>

                {/* Manager */}
                <div className="flex items-center gap-2 text-sm">
                  <User className="h-4 w-4" />
                  <span>
                    المدير: {team.manager?.full_name || 'غير محدد'}
                  </span>
                </div>

                {/* Members Count */}
                <div className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4" />
                  <span>
                    الأعضاء: {team.members?.length || 0}
                  </span>
                </div>

                {/* Created Date */}
                <div className="text-xs text-muted-foreground">
                  تم الإنشاء: {new Date(team.created_at).toLocaleDateString('ar-SA')}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {teams.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">لا توجد فرق</h3>
            <p className="text-muted-foreground mb-4">
              {selectedAreaFilter 
                ? 'لا توجد فرق في المنطقة المحددة' 
                : 'ابدأ بإنشاء فريق جديد لتنظيم المستخدمين'
              }
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)} className="cursor-pointer">
              <Plus className="h-4 w-4 ml-2" />
              إضافة فريق
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open)
        if (!open) resetForm()
      }}>
        <DialogContent className="sm:max-w-[500px] text-right" dir="rtl">
          <DialogHeader className="text-right">
            <DialogTitle>تعديل الفريق</DialogTitle>
            <DialogDescription>
              تعديل بيانات الفريق
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">اسم الفريق *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="أدخل اسم الفريق"
                className="text-right"
                dir="rtl"
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="edit-area">المنطقة *</Label>
              <Select value={formData.area_id} onValueChange={(value) => 
                setFormData(prev => ({ ...prev, area_id: value }))
              }>
                <SelectTrigger className="text-right" dir="rtl">
                  <SelectValue placeholder="اختر المنطقة" />
                </SelectTrigger>
                <SelectContent>
                  {areas.map((area) => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="edit-description">الوصف</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="أدخل وصف الفريق"
                className="text-right"
                dir="rtl"
                rows={3}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="edit-manager">مدير الفريق</Label>
              <Select value={formData.manager_id || "none"} onValueChange={(value) =>
                setFormData(prev => ({ ...prev, manager_id: value === "none" ? "" : value }))
              }>
                <SelectTrigger className="text-right" dir="rtl">
                  <SelectValue placeholder="اختر مدير الفريق" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">بدون مدير</SelectItem>
                  {availableManagers.map((manager) => (
                    <SelectItem key={manager.id} value={manager.id}>
                      <div className="flex items-center gap-2 text-right">
                        <span>{manager.full_name || manager.email}</span>
                        <Badge variant="outline" className="text-xs">
                          {getRoleArabicName(manager.role)}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex gap-2 justify-start">
              <Button type="submit" disabled={formLoading} className="cursor-pointer">
                {formLoading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsEditDialogOpen(false)}
                className="cursor-pointer"
              >
                إلغاء
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}

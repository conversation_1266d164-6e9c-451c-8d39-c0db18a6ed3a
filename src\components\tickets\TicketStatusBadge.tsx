'use client'

import { Badge } from '@/components/ui/badge'

interface TicketStatusBadgeProps {
  status: 'open' | 'in_progress' | 'closed'
  className?: string
}

export function TicketStatusBadge({ status, className }: TicketStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'open':
        return {
          label: 'مفتوح',
          variant: 'destructive' as const,
        }
      case 'in_progress':
        return {
          label: 'قيد المعالجة',
          variant: 'default' as const,
        }
      case 'closed':
        return {
          label: 'مغلق',
          variant: 'secondary' as const,
        }
      default:
        return {
          label: 'غير معروف',
          variant: 'outline' as const,
        }
    }
  }

  const config = getStatusConfig(status)

  return (
    <Badge variant={config.variant} className={className}>
      {config.label}
    </Badge>
  )
}

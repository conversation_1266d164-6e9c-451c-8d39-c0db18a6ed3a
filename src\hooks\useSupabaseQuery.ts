'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { createClient } from '@/lib/supabase'

interface UseSupabaseQueryOptions {
  timeout?: number
  retryAttempts?: number
  retryDelay?: number
  onError?: (error: any) => void
  onSuccess?: (data: any) => void
}

export function useSupabaseQuery<T>(
  queryFn: () => Promise<T>,
  dependencies: any[] = [],
  options: UseSupabaseQueryOptions = {}
) {
  const {
    timeout = 8000, // 8 second timeout
    retryAttempts = 2,
    retryDelay = 1000,
    onError,
    onSuccess
  } = options

  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  
  const abortControllerRef = useRef<AbortController>()
  const timeoutRef = useRef<NodeJS.Timeout>()
  const mountedRef = useRef(true)

  const executeQuery = useCallback(async (attempt = 0) => {
    if (!mountedRef.current) return

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Clear previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController()
    
    setLoading(true)
    setError(null)

    try {
      // Set timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        timeoutRef.current = setTimeout(() => {
          if (abortControllerRef.current) {
            abortControllerRef.current.abort()
          }
          reject(new Error('Request timeout - connection may be stale'))
        }, timeout)
      })

      // Execute query with timeout race
      const result = await Promise.race([
        queryFn(),
        timeoutPromise
      ])

      if (!mountedRef.current) return

      // Clear timeout on success
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      setData(result)
      setError(null)
      setRetryCount(0)
      onSuccess?.(result)

    } catch (err: any) {
      if (!mountedRef.current) return

      console.error(`Query attempt ${attempt + 1} failed:`, err)

      // Check if it's an abort (user navigated away)
      if (err.name === 'AbortError') {
        return
      }

      const errorMessage = err.message || 'حدث خطأ في تحميل البيانات'

      // Retry logic
      if (attempt < retryAttempts && !err.message?.includes('timeout')) {
        console.log(`Retrying query in ${retryDelay}ms... (attempt ${attempt + 2}/${retryAttempts + 1})`)
        setRetryCount(attempt + 1)
        
        setTimeout(() => {
          if (mountedRef.current) {
            executeQuery(attempt + 1)
          }
        }, retryDelay * (attempt + 1)) // Exponential backoff
        
        return
      }

      // Final failure
      setError(errorMessage)
      setRetryCount(0)
      onError?.(err)
    } finally {
      if (mountedRef.current) {
        setLoading(false)
      }
    }
  }, [queryFn, timeout, retryAttempts, retryDelay, onError, onSuccess])

  // Retry function for manual retries
  const retry = useCallback(() => {
    executeQuery(0)
  }, [executeQuery])

  // Execute query when dependencies change
  useEffect(() => {
    executeQuery(0)
  }, dependencies)

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false
      
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // Handle visibility change (browser focus)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && error) {
        // Browser regained focus and there was an error, retry
        console.log('Browser regained focus, retrying failed query...')
        setTimeout(() => retry(), 1000)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [error, retry])

  return {
    data,
    loading,
    error,
    retry,
    retryCount,
    isRetrying: retryCount > 0
  }
}
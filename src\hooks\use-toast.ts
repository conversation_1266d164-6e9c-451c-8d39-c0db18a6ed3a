"use client"

import React from "react"
import { toast as sonnerToast } from "sonner"
import { CheckCircle, XCircle, AlertCircle, Info } from "lucide-react"

interface ToastOptions {
  description?: string
  action?: {
    label: string
    onClick: () => void
  }
  duration?: number
}

export const useToast = () => {
  const toast = {
    success: (message: string, options?: ToastOptions) => {
      return sonnerToast.success(message, {
        description: options?.description,
        action: options?.action,
        duration: options?.duration || 4000,
        icon: React.createElement(CheckCircle, { className: "h-4 w-4" }),
        style: {
          direction: 'rtl',
          textAlign: 'right',
        },
      })
    },

    error: (message: string, options?: ToastOptions) => {
      return sonnerToast.error(message, {
        description: options?.description,
        action: options?.action,
        duration: options?.duration || 5000,
        icon: React.createElement(XCircle, { className: "h-4 w-4" }),
        style: {
          direction: 'rtl',
          textAlign: 'right',
        },
      })
    },

    warning: (message: string, options?: ToastOptions) => {
      return sonnerToast.warning(message, {
        description: options?.description,
        action: options?.action,
        duration: options?.duration || 4000,
        icon: React.createElement(AlertCircle, { className: "h-4 w-4" }),
        style: {
          direction: 'rtl',
          textAlign: 'right',
        },
      })
    },

    info: (message: string, options?: ToastOptions) => {
      return sonnerToast.info(message, {
        description: options?.description,
        action: options?.action,
        duration: options?.duration || 4000,
        icon: React.createElement(Info, { className: "h-4 w-4" }),
        style: {
          direction: 'rtl',
          textAlign: 'right',
        },
      })
    },

    loading: (message: string, options?: Omit<ToastOptions, 'duration'>) => {
      return sonnerToast.loading(message, {
        description: options?.description,
        action: options?.action,
        style: {
          direction: 'rtl',
          textAlign: 'right',
        },
      })
    },

    promise: <T,>(
      promise: Promise<T>,
      {
        loading,
        success,
        error,
      }: {
        loading: string
        success: string | ((data: T) => string)
        error: string | ((error: any) => string)
      }
    ) => {
      return sonnerToast.promise(promise, {
        loading,
        success,
        error,
        style: {
          direction: 'rtl',
          textAlign: 'right',
        },
      })
    },

    dismiss: (toastId?: string | number) => {
      return sonnerToast.dismiss(toastId)
    },
  }

  return { toast }
}

// Export individual toast functions for convenience
export const toast = {
  success: (message: string, options?: ToastOptions) => {
    return sonnerToast.success(message, {
      description: options?.description,
      action: options?.action,
      duration: options?.duration || 4000,
      icon: React.createElement(CheckCircle, { className: "h-4 w-4" }),
      style: {
        direction: 'rtl',
        textAlign: 'right',
      },
    })
  },

  error: (message: string, options?: ToastOptions) => {
    return sonnerToast.error(message, {
      description: options?.description,
      action: options?.action,
      duration: options?.duration || 5000,
      icon: React.createElement(XCircle, { className: "h-4 w-4" }),
      style: {
        direction: 'rtl',
        textAlign: 'right',
      },
    })
  },

  warning: (message: string, options?: ToastOptions) => {
    return sonnerToast.warning(message, {
      description: options?.description,
      action: options?.action,
      duration: options?.duration || 4000,
      icon: React.createElement(AlertCircle, { className: "h-4 w-4" }),
      style: {
        direction: 'rtl',
        textAlign: 'right',
      },
    })
  },

  info: (message: string, options?: ToastOptions) => {
    return sonnerToast.info(message, {
      description: options?.description,
      action: options?.action,
      duration: options?.duration || 4000,
      icon: React.createElement(Info, { className: "h-4 w-4" }),
      style: {
        direction: 'rtl',
        textAlign: 'right',
      },
    })
  },

  loading: (message: string, options?: Omit<ToastOptions, 'duration'>) => {
    return sonnerToast.loading(message, {
      description: options?.description,
      action: options?.action,
      style: {
        direction: 'rtl',
        textAlign: 'right',
      },
    })
  },

  promise: <T,>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading,
      success,
      error,
      style: {
        direction: 'rtl',
        textAlign: 'right',
      },
    })
  },

  dismiss: (toastId?: string | number) => {
    return sonnerToast.dismiss(toastId)
  },
}

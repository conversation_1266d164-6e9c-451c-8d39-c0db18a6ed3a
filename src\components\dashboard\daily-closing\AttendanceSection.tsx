'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Clock, CheckCircle } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/supabase'

type DailyClosing = Database['public']['Tables']['daily_closings']['Row']
type AttendanceRecord = Database['public']['Tables']['attendance_records']['Row']

interface AttendanceSectionProps {
  dailyClosing: DailyClosing
  onUpdate: () => void
}

export function AttendanceSection({ dailyClosing, onUpdate }: AttendanceSectionProps) {
  const [attendanceRecord, setAttendanceRecord] = useState<AttendanceRecord | null>(null)
  const [checkInTime, setCheckInTime] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const supabase = createClient()

  useEffect(() => {
    fetchAttendanceRecord()
  }, [dailyClosing.id])

  const fetchAttendanceRecord = async () => {
    try {
      const { data, error } = await supabase
        .from('attendance_records')
        .select('*')
        .eq('daily_closing_id', dailyClosing.id)
        .single()

      if (data) {
        setAttendanceRecord(data)
        setCheckInTime(data.check_in_time)
      } else if (error && error.code !== 'PGRST116') {
        console.error('Error fetching attendance record:', error)
      }
    } catch (error) {
      console.error('Error in fetchAttendanceRecord:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!checkInTime) {
      setError('يرجى إدخال وقت الحضور')
      setLoading(false)
      return
    }

    try {
      if (attendanceRecord) {
        // Update existing record
        const { error } = await supabase
          .from('attendance_records')
          .update({
            check_in_time: checkInTime,
            updated_at: new Date().toISOString()
          })
          .eq('id', attendanceRecord.id)

        if (error) throw error
      } else {
        // Create new record
        const { error } = await supabase
          .from('attendance_records')
          .insert({
            daily_closing_id: dailyClosing.id,
            check_in_time: checkInTime
          })

        if (error) throw error
      }

      // Update daily closing to mark attendance as submitted
      const { error: updateError } = await supabase
        .from('daily_closings')
        .update({
          attendance_submitted: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', dailyClosing.id)

      if (updateError) throw updateError

      // Refresh the attendance record to get the latest data
      await fetchAttendanceRecord()

      // Notify parent component to refresh
      onUpdate()
    } catch (error: any) {
      console.error('Error submitting attendance:', error)
      setError('حدث خطأ في حفظ بيانات الحضور')
    } finally {
      setLoading(false)
    }
  }

  const getCurrentTime = () => {
    const now = new Date()
    const hours = now.getHours().toString().padStart(2, '0')
    const minutes = now.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }

  const fillCurrentTime = () => {
    setCheckInTime(getCurrentTime())
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          تسجيل الحضور
          {dailyClosing.attendance_submitted && (
            <CheckCircle className="h-5 w-5 text-green-600" />
          )}
        </CardTitle>
        <CardDescription>
          سجل وقت حضورك لبدء اليوم
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4" dir="rtl">
          <div className="space-y-2">
            <Label htmlFor="checkInTime">وقت الحضور</Label>
            <div className="flex gap-2">
              <Input
                id="checkInTime"
                type="time"
                value={checkInTime}
                onChange={(e) => setCheckInTime(e.target.value)}
                disabled={dailyClosing.attendance_submitted}
                className="flex-1"
                required
              />
              {!dailyClosing.attendance_submitted && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={fillCurrentTime}
                  className="whitespace-nowrap"
                >
                  الوقت الحالي
                </Button>
              )}
            </div>
          </div>

          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
              {error}
            </div>
          )}

          {dailyClosing.attendance_submitted ? (
            <div className="flex items-center gap-2 text-green-600 bg-green-50 p-3 rounded-md">
              <CheckCircle className="h-4 w-4" />
              <span>تم تسجيل الحضور بنجاح</span>
            </div>
          ) : (
            <Button 
              type="submit" 
              disabled={loading}
              className="w-full"
            >
              {loading ? 'جاري الحفظ...' : 'تسجيل الحضور'}
            </Button>
          )}
        </form>
      </CardContent>
    </Card>
  )
}

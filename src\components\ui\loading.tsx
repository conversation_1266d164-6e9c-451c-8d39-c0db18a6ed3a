import { cn } from "@/lib/utils"

interface LoadingProps {
  className?: string
  size?: "sm" | "md" | "lg" | "xl"
  text?: string
  showText?: boolean
  variant?: "spinner" | "dots" | "pulse" | "bars"
}

export function Loading({ 
  className, 
  size = "md", 
  text, 
  showText = false, 
  variant = "spinner" 
}: LoadingProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8", 
    lg: "h-12 w-12",
    xl: "h-16 w-16"
  }

  const dotSizes = {
    sm: "h-1 w-1",
    md: "h-2 w-2",
    lg: "h-3 w-3", 
    xl: "h-4 w-4"
  }

  const renderLoader = () => {
    switch (variant) {
      case "dots":
        return (
          <div className="flex items-center gap-1">
            <div className={cn("bg-primary rounded-full animate-bounce-smooth", dotSizes[size])} 
                 style={{ animationDelay: "0ms" }} />
            <div className={cn("bg-primary rounded-full animate-bounce-smooth", dotSizes[size])} 
                 style={{ animationDelay: "200ms" }} />
            <div className={cn("bg-primary rounded-full animate-bounce-smooth", dotSizes[size])} 
                 style={{ animationDelay: "400ms" }} />
          </div>
        )
      
      case "pulse":
        return (
          <div className={cn(
            "bg-primary rounded-full animate-pulse opacity-75",
            sizeClasses[size]
          )} />
        )
      
      case "bars":
        return (
          <div className="flex items-end gap-1">
            <div className={cn("bg-primary animate-pulse", size === "sm" ? "h-3 w-1" : size === "md" ? "h-6 w-1.5" : size === "lg" ? "h-8 w-2" : "h-10 w-2.5")} 
                 style={{ animationDelay: "0ms", animationDuration: "1s" }} />
            <div className={cn("bg-primary animate-pulse", size === "sm" ? "h-4 w-1" : size === "md" ? "h-8 w-1.5" : size === "lg" ? "h-10 w-2" : "h-12 w-2.5")} 
                 style={{ animationDelay: "200ms", animationDuration: "1s" }} />
            <div className={cn("bg-primary animate-pulse", size === "sm" ? "h-2 w-1" : size === "md" ? "h-4 w-1.5" : size === "lg" ? "h-6 w-2" : "h-8 w-2.5")} 
                 style={{ animationDelay: "400ms", animationDuration: "1s" }} />
          </div>
        )
      
      default: // spinner
        return (
          <div className={cn(
            "animate-spin rounded-full border-2 border-primary/20 border-t-primary transition-all duration-300",
            sizeClasses[size]
          )} />
        )
    }
  }

  return (
    <div className={cn("flex flex-col items-center justify-center gap-3", className)}>
      {renderLoader()}
      {showText && (
        <p className="text-sm text-muted-foreground animate-pulse">
          {text || "جاري التحميل..."}
        </p>
      )}
    </div>
  )
}

export function LoadingPage({ text, variant = "spinner" }: { text?: string; variant?: "spinner" | "dots" | "pulse" | "bars" }) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center transition-all duration-300">
      <div className="text-center space-y-8">
        {/* Main loader */}
        <div className="relative">
          <Loading size="xl" variant={variant} />
          {variant === "spinner" && (
            <div className="absolute inset-0 animate-ping rounded-full border border-primary/10" />
          )}
        </div>
        
        {/* Text and secondary animation */}
        <div className="space-y-4">
          <p className="text-lg font-medium text-foreground">
            {text || "جاري التحميل..."}
          </p>
          
        </div>
      </div>
    </div>
  )
}

export function QuickLoading({ text, variant = "dots" }: { text?: string; variant?: "spinner" | "dots" | "pulse" | "bars" }) {
  return (
    <div className="flex items-center justify-center p-6 transition-all duration-200">
      <Loading size="md" variant={variant} text={text} showText={!!text} />
    </div>
  )
}

export function InlineLoading({ text, className, variant = "dots" }: { text?: string; className?: string; variant?: "spinner" | "dots" | "pulse" | "bars" }) {
  return (
    <div className={cn("flex items-center gap-3 text-sm text-muted-foreground", className)}>
      <Loading size="sm" variant={variant} />
      <span>{text || "جاري التحميل..."}</span>
    </div>
  )
}

// Modern card loader with shimmer effect
export function CardLoader() {
  return (
    <div className="rounded-lg border bg-card p-6 animate-pulse">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="h-5 bg-gradient-to-r from-muted via-muted/50 to-muted rounded w-1/3 animate-shimmer bg-[length:200%_100%]"></div>
          <div className="h-4 w-4 bg-muted rounded"></div>
        </div>
        
        {/* Content lines */}
        <div className="space-y-2">
          <div className="h-4 bg-gradient-to-r from-muted via-muted/50 to-muted rounded w-full animate-shimmer bg-[length:200%_100%]"></div>
          <div className="h-4 bg-gradient-to-r from-muted via-muted/50 to-muted rounded w-4/5 animate-shimmer bg-[length:200%_100%]"></div>
          <div className="h-4 bg-gradient-to-r from-muted via-muted/50 to-muted rounded w-2/3 animate-shimmer bg-[length:200%_100%]"></div>
        </div>
        
        {/* Footer */}
        <div className="flex items-center justify-between pt-2">
          <div className="h-3 bg-muted rounded w-1/4"></div>
          <div className="h-6 bg-muted rounded w-16"></div>
        </div>
      </div>
    </div>
  )
}

// Skeleton loaders for better UX
export function SkeletonCard() {
  return <CardLoader />
}

export function SkeletonTable() {
  return (
    <div className="space-y-3">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4 rtl:space-x-reverse animate-pulse">
          <div className="h-10 w-10 bg-muted rounded-full"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </div>
          <div className="h-8 w-20 bg-muted rounded"></div>
        </div>
      ))}
    </div>
  )
}

export function SkeletonForm() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="space-y-2">
        <div className="h-4 bg-muted rounded w-1/4"></div>
        <div className="h-10 bg-muted rounded"></div>
      </div>
      <div className="space-y-2">
        <div className="h-4 bg-muted rounded w-1/3"></div>
        <div className="h-24 bg-muted rounded"></div>
      </div>
      <div className="h-10 bg-muted rounded w-32"></div>
    </div>
  )
}

export function SkeletonPage() {
  return (
    <div className="space-y-6 animate-pulse">
      {/* Header */}
      <div className="space-y-2">
        <div className="h-8 bg-muted rounded w-1/3"></div>
        <div className="h-4 bg-muted rounded w-1/2"></div>
      </div>
      
      {/* Content */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <SkeletonCard key={i} />
        ))}
      </div>
    </div>
  )
}

export function SkeletonDashboard() {
  return (
    <div className="space-y-6 animate-pulse">
      {/* Welcome section */}
      <div className="space-y-2">
        <div className="h-10 bg-muted rounded w-1/2"></div>
        <div className="h-4 bg-muted rounded w-3/4"></div>
      </div>
      
      {/* Stats cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="rounded-lg border bg-card p-6">
            <div className="space-y-3">
              <div className="h-4 bg-muted rounded w-1/2"></div>
              <div className="h-8 bg-muted rounded w-1/3"></div>
              <div className="h-3 bg-muted rounded w-2/3"></div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Main content */}
      <div className="grid gap-6 lg:grid-cols-2">
        <SkeletonCard />
        <SkeletonCard />
      </div>
    </div>
  )
}

import { requireAuth, getUserProfile } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { AreasManagement } from '@/components/admin/AreasManagement'
import { redirect } from 'next/navigation'
import { isSystemAdmin } from '@/lib/roles'

export default async function AreasPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  // Check if user is system admin
  if (!profile || !isSystemAdmin(profile.role)) {
    redirect('/unauthorized')
  }

  return (
    <DashboardLayout user={{
      name: profile.full_name || user.email,
      email: user.email,
      role: profile.role
    }}>
      <AreasManagement />
    </DashboardLayout>
  )
}

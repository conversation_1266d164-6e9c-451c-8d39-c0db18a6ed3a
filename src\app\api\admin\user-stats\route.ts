import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { isSystemAdmin } from '@/lib/roles'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    // Get user's profile to check permissions
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 })
    }

    // Check if user is system admin
    if (!isSystemAdmin(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get user statistics by role
    const { data: stats, error: statsError } = await supabase
      .from('profiles')
      .select('role')

    if (statsError) {
      console.error('Error fetching user stats:', statsError)
      return NextResponse.json({ error: 'Failed to fetch statistics' }, { status: 500 })
    }

    // Count users by role
    const roleStats: Record<string, number> = {}
    stats?.forEach((user) => {
      roleStats[user.role] = (roleStats[user.role] || 0) + 1
    })

    return NextResponse.json(roleStats)
    
  } catch (error) {
    console.error('Unexpected error in user stats API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

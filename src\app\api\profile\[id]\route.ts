import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, getUserProfile } from '@/lib/auth'

// Create admin client with service role key for broader data access
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require authentication
    const currentUser = await requireAuth()
    const currentProfile = await getUserProfile()
    
    if (!currentProfile) {
      return NextResponse.json(
        { error: 'ملف المستخدم غير موجود' },
        { status: 404 }
      )
    }

    const { id } = await params

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'معرف المستخدم غير صحيح' },
        { status: 400 }
      )
    }

    // Check access permissions
    const canViewProfile = (
      // Users can always view their own profile
      currentProfile.id === id ||
      // System admins can view any profile
      currentProfile.role === 'system_admin' ||
      // Area managers can view profiles in their area
      (currentProfile.role === 'area_manager' && currentProfile.area_id) ||
      // Team managers can view profiles in their team
      (currentProfile.role === 'team_manager' && currentProfile.team_id)
    )

    if (!canViewProfile) {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لعرض هذا الملف الشخصي' },
        { status: 403 }
      )
    }

    // Get target user profile with related data
    const { data: targetProfile, error } = await supabaseAdmin
      .from('profiles')
      .select(`
        *,
        team:teams!profiles_team_id_fkey(
          id,
          name,
          area:areas!teams_area_id_fkey(id, name)
        ),
        area:areas!profiles_area_id_fkey(id, name)
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'المستخدم غير موجود' },
          { status: 404 }
        )
      }
      console.error('Error fetching profile:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب بيانات المستخدم' },
        { status: 500 }
      )
    }

    // Additional access control checks based on role hierarchy
    if (currentProfile.role !== 'system_admin' && currentProfile.id !== id) {
      // Area managers can only view users in their area
      if (currentProfile.role === 'area_manager') {
        const targetInSameArea = (
          targetProfile.area_id === currentProfile.area_id ||
          targetProfile.team?.area?.id === currentProfile.area_id
        )
        
        if (!targetInSameArea) {
          return NextResponse.json(
            { error: 'ليس لديك صلاحية لعرض هذا الملف الشخصي' },
            { status: 403 }
          )
        }
      }
      
      // Team managers can only view users in their team
      if (currentProfile.role === 'team_manager') {
        const targetInSameTeam = targetProfile.team_id === currentProfile.team_id
        
        if (!targetInSameTeam) {
          return NextResponse.json(
            { error: 'ليس لديك صلاحية لعرض هذا الملف الشخصي' },
            { status: 403 }
          )
        }
      }
    }

    return NextResponse.json({ user: targetProfile })

  } catch (error: any) {
    console.error('Profile API error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

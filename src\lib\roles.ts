import { UserRole, RoleLevel, ROLE_HIERARCHY, RoleInfo } from './supabase'

/**
 * Role hierarchy utility functions for permission management
 */

/**
 * Check if a role has higher or equal privileges than another role
 */
export function hasHigherOrEqualRole(userRole: UserRole | string | undefined, requiredRole: UserRole | string | undefined): boolean {
  if (!userRole || !requiredRole || typeof userRole !== 'string' || typeof requiredRole !== 'string') {
    return false
  }

  const userRoleInfo = ROLE_HIERARCHY[userRole as UserRole]
  const requiredRoleInfo = ROLE_HIERARCHY[requiredRole as UserRole]

  if (!userRoleInfo || !requiredRoleInfo) {
    return false
  }

  return userRoleInfo.level <= requiredRoleInfo.level // Lower level number = higher privileges
}

/**
 * Check if a role level has higher or equal privileges than required level
 */
export function hasHigherOrEqualRoleLevel(userLevel: RoleLevel | number | undefined, requiredLevel: RoleLevel | number | undefined): boolean {
  if (typeof userLevel !== 'number' || typeof requiredLevel !== 'number') {
    return false
  }
  return userLevel <= requiredLevel // Lower level number = higher privileges
}

/**
 * Get role information by role name
 */
export function getRoleInfo(role: UserRole | string | undefined): RoleInfo | null {
  if (!role || typeof role !== 'string') {
    return null
  }
  return ROLE_HIERARCHY[role as UserRole] || null
}

/**
 * Get Arabic name for a role
 */
export function getRoleArabicName(role: UserRole | string | undefined): string {
  if (!role || typeof role !== 'string') {
    return 'غير محدد'
  }

  const roleInfo = ROLE_HIERARCHY[role as UserRole]
  return roleInfo?.arabicName || 'غير محدد'
}

/**
 * Get all roles that are at or below a certain level
 */
export function getRolesAtOrBelowLevel(level: RoleLevel | number | undefined): UserRole[] {
  if (typeof level !== 'number') {
    return []
  }

  return Object.keys(ROLE_HIERARCHY).filter(
    role => ROLE_HIERARCHY[role as UserRole].level >= level
  ) as UserRole[]
}

/**
 * Get all roles that are above a certain level
 */
export function getRolesAboveLevel(level: RoleLevel | number | undefined): UserRole[] {
  if (typeof level !== 'number') {
    return []
  }

  return Object.keys(ROLE_HIERARCHY).filter(
    role => ROLE_HIERARCHY[role as UserRole].level < level
  ) as UserRole[]
}

/**
 * Check if user can manage another user based on role hierarchy
 */
export function canManageUser(managerRole: UserRole | string | undefined, targetRole: UserRole | string | undefined): boolean {
  if (!managerRole || !targetRole || typeof managerRole !== 'string' || typeof targetRole !== 'string') {
    return false
  }

  const managerRoleInfo = ROLE_HIERARCHY[managerRole as UserRole]
  const targetRoleInfo = ROLE_HIERARCHY[targetRole as UserRole]

  if (!managerRoleInfo || !targetRoleInfo) {
    return false
  }

  // Can manage users at same level or below (higher level number)
  return managerRoleInfo.level <= targetRoleInfo.level
}

/**
 * Check if user can view another user based on role hierarchy
 */
export function canViewUser(viewerRole: UserRole | string | undefined, targetRole: UserRole | string | undefined): boolean {
  // Same logic as canManageUser for now, but can be customized later
  return canManageUser(viewerRole, targetRole)
}

/**
 * Get the highest role (lowest level number) from an array of roles
 */
export function getHighestRole(roles: (UserRole | string)[]): UserRole | null {
  if (roles.length === 0) return null

  const validRoles = roles.filter(role =>
    role && typeof role === 'string' && ROLE_HIERARCHY[role as UserRole]
  ) as UserRole[]

  if (validRoles.length === 0) return null

  return validRoles.reduce((highest, current) => {
    const currentInfo = ROLE_HIERARCHY[current]
    const highestInfo = ROLE_HIERARCHY[highest]
    return currentInfo.level < highestInfo.level ? current : highest
  })
}

/**
 * Get the lowest role (highest level number) from an array of roles
 */
export function getLowestRole(roles: (UserRole | string)[]): UserRole | null {
  if (roles.length === 0) return null

  const validRoles = roles.filter(role =>
    role && typeof role === 'string' && ROLE_HIERARCHY[role as UserRole]
  ) as UserRole[]

  if (validRoles.length === 0) return null

  return validRoles.reduce((lowest, current) => {
    const currentInfo = ROLE_HIERARCHY[current]
    const lowestInfo = ROLE_HIERARCHY[lowest]
    return currentInfo.level > lowestInfo.level ? current : lowest
  })
}

/**
 * Check if a role has a specific permission
 */
export function hasPermission(role: UserRole | string | undefined, permission: string): boolean {
  if (!role || typeof role !== 'string' || !permission) {
    return false
  }

  const roleInfo = ROLE_HIERARCHY[role as UserRole]
  if (!roleInfo) {
    return false
  }

  // System admin has all permissions
  if (role === 'system_admin') return true

  return roleInfo.permissions.includes(permission)
}

/**
 * Get all available roles for selection (useful for dropdowns)
 */
export function getAvailableRoles(): { value: UserRole; label: string; level: RoleLevel }[] {
  return Object.entries(ROLE_HIERARCHY).map(([role, info]) => ({
    value: role as UserRole,
    label: info.arabicName,
    level: info.level
  })).sort((a, b) => a.level - b.level) // Sort by hierarchy level
}

/**
 * Get roles that a user can assign to others
 */
export function getAssignableRoles(userRole: UserRole | string | undefined): UserRole[] {
  if (!userRole || typeof userRole !== 'string') {
    return []
  }

  const userRoleInfo = ROLE_HIERARCHY[userRole as UserRole]
  if (!userRoleInfo) {
    return []
  }

  // System admin can assign any role except system_admin to others
  if (userRole === 'system_admin') {
    return ['area_manager', 'team_manager', 'sales_employee']
  }

  // Other roles can only assign roles at their level or below
  return Object.keys(ROLE_HIERARCHY).filter(
    role => ROLE_HIERARCHY[role as UserRole].level >= userRoleInfo.level
  ) as UserRole[]
}

/**
 * Validate if a role assignment is allowed
 */
export function canAssignRole(assignerRole: UserRole | string | undefined, targetRole: UserRole | string | undefined): boolean {
  if (!assignerRole || !targetRole || typeof assignerRole !== 'string' || typeof targetRole !== 'string') {
    return false
  }

  const assignableRoles = getAssignableRoles(assignerRole)
  return assignableRoles.includes(targetRole as UserRole)
}

/**
 * Get role badge variant for UI components
 */
export function getRoleBadgeVariant(role: UserRole | string | undefined): 'default' | 'secondary' | 'destructive' | 'outline' {
  if (!role || typeof role !== 'string') {
    return 'outline'
  }

  switch (role) {
    case 'system_admin':
      return 'destructive' // Red for highest privilege
    case 'area_manager':
      return 'default' // Blue for high privilege
    case 'team_manager':
      return 'secondary' // Gray for medium privilege
    case 'sales_employee':
      return 'outline' // Outline for lowest privilege
    default:
      return 'outline'
  }
}

/**
 * Check if user is system admin
 */
export function isSystemAdmin(role: UserRole | string | undefined): boolean {
  return role === 'system_admin'
}

/**
 * Check if user is area manager or higher
 */
export function isAreaManagerOrHigher(role: UserRole | string | undefined): boolean {
  return hasHigherOrEqualRole(role, 'area_manager')
}

/**
 * Check if user is team manager or higher
 */
export function isTeamManagerOrHigher(role: UserRole | string | undefined): boolean {
  return hasHigherOrEqualRole(role, 'team_manager')
}

/**
 * Get role hierarchy level as string for display
 */
export function getRoleLevelDisplay(level: RoleLevel): string {
  const levelNames = {
    1: 'المستوى الأول',
    2: 'المستوى الثاني', 
    3: 'المستوى الثالث',
    4: 'المستوى الرابع'
  }
  return levelNames[level]
}

/**
 * Sort users by role hierarchy (highest role first)
 */
export function sortUsersByRole<T extends { role: UserRole | string | undefined }>(users: T[]): T[] {
  return users.sort((a, b) => {
    const aRoleInfo = a.role && typeof a.role === 'string' ? ROLE_HIERARCHY[a.role as UserRole] : null
    const bRoleInfo = b.role && typeof b.role === 'string' ? ROLE_HIERARCHY[b.role as UserRole] : null

    // If either role is invalid, put it at the end
    if (!aRoleInfo && !bRoleInfo) return 0
    if (!aRoleInfo) return 1
    if (!bRoleInfo) return -1

    return aRoleInfo.level - bRoleInfo.level
  })
}

'use server'

import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

export async function logoutAction() {
  const cookieStore = await cookies()

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  try {
    console.log('Server action: Starting logout process')

    // Clear all auth-related cookies manually
    const authCookies = [
      'sb-access-token',
      'sb-refresh-token',
      'supabase-auth-token',
      'supabase.auth.token'
    ]

    authCookies.forEach(cookieName => {
      cookieStore.delete(cookieName)
    })

    // Always attempt to sign out, but don't throw on errors
    const { error } = await supabase.auth.signOut()

    if (error) {
      console.error('Server action: Logout error (continuing anyway):', error)
      // Don't throw - continue with redirect even if logout has issues
    } else {
      console.log('Server action: Logout successful')
    }

  } catch (error) {
    console.error('Server action: Unexpected error (continuing anyway):', error)
    // Don't throw - continue with redirect even if there are unexpected errors
  }

  // Always redirect to login page regardless of logout success/failure
  redirect('/login')
}

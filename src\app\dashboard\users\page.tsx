import { requireAuth, getUserProfile } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { UserManagementPage } from '@/components/admin/UserManagementPage'
import { redirect } from 'next/navigation'
import { isSystemAdmin } from '@/lib/roles'

export default async function AdminUsersPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  // Check if user is system admin
  if (!profile || !isSystemAdmin(profile.role)) {
    redirect('/unauthorized')
  }

  return (
    <DashboardLayout user={{
      name: profile.full_name || user.email,
      email: user.email,
      role: profile.role
    }}>
      <UserManagementPage />
    </DashboardLayout>
  )
}

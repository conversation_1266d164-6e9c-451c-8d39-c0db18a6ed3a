-- Create ticket system for sales employees to communicate with team managers
-- Sales employees can create tickets that are automatically assigned to their team manager

-- Create tickets table
CREATE TABLE IF NOT EXISTS public.tickets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    status TEXT CHECK (status IN ('open', 'in_progress', 'closed')) DEFAULT 'open',
    priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
    created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    assigned_to UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create ticket_replies table
CREATE TABLE IF NOT EXISTS public.ticket_replies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ticket_id UUID REFERENCES public.tickets(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create updated_at trigger for tickets
CREATE TRIGGER handle_tickets_updated_at
    BEFORE UPDATE ON public.tickets
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Enable Row Level Security
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ticket_replies ENABLE ROW LEVEL SECURITY;

-- RLS Policies for tickets table

-- System admins can manage all tickets
CREATE POLICY "System admins can manage all tickets" ON public.tickets
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- Sales employees can view and create their own tickets
CREATE POLICY "Sales employees can manage own tickets" ON public.tickets
    FOR ALL USING (
        created_by = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'sales_employee'
        )
    );

-- Team managers can view tickets from their team members
CREATE POLICY "Team managers can view team tickets" ON public.tickets
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            JOIN public.teams t ON p.team_id = t.id
            WHERE p.id = tickets.created_by 
            AND t.manager_id = auth.uid()
            AND EXISTS (
                SELECT 1 FROM public.profiles pm
                WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
            )
        )
    );

-- Team managers can update status of tickets from their team members
CREATE POLICY "Team managers can update team tickets" ON public.tickets
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            JOIN public.teams t ON p.team_id = t.id
            WHERE p.id = tickets.created_by 
            AND t.manager_id = auth.uid()
            AND EXISTS (
                SELECT 1 FROM public.profiles pm
                WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
            )
        )
    );

-- Area managers can view tickets from their area teams
CREATE POLICY "Area managers can view area tickets" ON public.tickets
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            JOIN public.teams t ON p.team_id = t.id
            JOIN public.areas a ON t.area_id = a.id
            WHERE p.id = tickets.created_by 
            AND a.manager_id = auth.uid()
            AND EXISTS (
                SELECT 1 FROM public.profiles am
                WHERE am.id = auth.uid() AND am.role = 'area_manager'
            )
        )
    );

-- RLS Policies for ticket_replies table

-- System admins can manage all ticket replies
CREATE POLICY "System admins can manage all ticket replies" ON public.ticket_replies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- Users can view replies for tickets they have access to
CREATE POLICY "Users can view accessible ticket replies" ON public.ticket_replies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.tickets t
            WHERE t.id = ticket_replies.ticket_id
            AND (
                -- Ticket creator can see replies
                t.created_by = auth.uid()
                OR
                -- Team manager can see replies for their team's tickets
                EXISTS (
                    SELECT 1 FROM public.profiles p
                    JOIN public.teams tm ON p.team_id = tm.id
                    WHERE p.id = t.created_by 
                    AND tm.manager_id = auth.uid()
                    AND EXISTS (
                        SELECT 1 FROM public.profiles pm
                        WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
                    )
                )
                OR
                -- Area manager can see replies for their area's tickets
                EXISTS (
                    SELECT 1 FROM public.profiles p
                    JOIN public.teams tm ON p.team_id = tm.id
                    JOIN public.areas a ON tm.area_id = a.id
                    WHERE p.id = t.created_by 
                    AND a.manager_id = auth.uid()
                    AND EXISTS (
                        SELECT 1 FROM public.profiles am
                        WHERE am.id = auth.uid() AND am.role = 'area_manager'
                    )
                )
            )
        )
    );

-- Users can create replies for tickets they have access to
CREATE POLICY "Users can create replies for accessible tickets" ON public.ticket_replies
    FOR INSERT WITH CHECK (
        user_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM public.tickets t
            WHERE t.id = ticket_replies.ticket_id
            AND (
                -- Ticket creator can reply
                t.created_by = auth.uid()
                OR
                -- Team manager can reply to their team's tickets
                EXISTS (
                    SELECT 1 FROM public.profiles p
                    JOIN public.teams tm ON p.team_id = tm.id
                    WHERE p.id = t.created_by 
                    AND tm.manager_id = auth.uid()
                    AND EXISTS (
                        SELECT 1 FROM public.profiles pm
                        WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
                    )
                )
                OR
                -- Area manager can reply to their area's tickets
                EXISTS (
                    SELECT 1 FROM public.profiles p
                    JOIN public.teams tm ON p.team_id = tm.id
                    JOIN public.areas a ON tm.area_id = a.id
                    WHERE p.id = t.created_by 
                    AND a.manager_id = auth.uid()
                    AND EXISTS (
                        SELECT 1 FROM public.profiles am
                        WHERE am.id = auth.uid() AND am.role = 'area_manager'
                    )
                )
            )
        )
    );

-- Function to automatically assign tickets to team manager when created
CREATE OR REPLACE FUNCTION public.assign_ticket_to_team_manager()
RETURNS TRIGGER AS $$
BEGIN
    -- Get the team manager for the user who created the ticket
    UPDATE public.tickets 
    SET assigned_to = (
        SELECT t.manager_id
        FROM public.profiles p
        JOIN public.teams t ON p.team_id = t.id
        WHERE p.id = NEW.created_by
    )
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to auto-assign tickets
CREATE TRIGGER assign_ticket_to_team_manager_trigger
    AFTER INSERT ON public.tickets
    FOR EACH ROW
    EXECUTE FUNCTION public.assign_ticket_to_team_manager();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tickets_created_by ON public.tickets(created_by);
CREATE INDEX IF NOT EXISTS idx_tickets_assigned_to ON public.tickets(assigned_to);
CREATE INDEX IF NOT EXISTS idx_tickets_status ON public.tickets(status);
CREATE INDEX IF NOT EXISTS idx_ticket_replies_ticket_id ON public.ticket_replies(ticket_id);
CREATE INDEX IF NOT EXISTS idx_ticket_replies_user_id ON public.ticket_replies(user_id);

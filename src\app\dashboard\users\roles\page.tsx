'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { RolesAndPermissions } from '@/components/admin/RolesAndPermissions'
import { isSystemAdmin } from '@/lib/roles'

export default function RolesAndPermissionsPage() {
  const { user, profile, loading } = useAuth()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)

  useEffect(() => {
    if (loading) return

    if (!user || !profile) {
      router.push('/login')
      return
    }

    if (!isSystemAdmin(profile.role)) {
      router.push('/unauthorized')
      return
    }

    setIsAuthorized(true)
  }, [user, profile, loading, router])

  if (loading || !isAuthorized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout user={{
      name: profile.full_name,
      email: profile.email,
      role: profile.role
    }}>
      <div className="space-y-6" dir="rtl">
        <div className="text-right">
          <h2 className="text-3xl font-bold">الأدوار والصلاحيات</h2>
          <p className="text-muted-foreground mt-2">
            إدارة أدوار المستخدمين وصلاحياتهم في النظام
          </p>
        </div>

        <RolesAndPermissions />
      </div>
    </DashboardLayout>
  )
}

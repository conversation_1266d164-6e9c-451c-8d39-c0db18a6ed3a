import { requireAuth, getUserProfile } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { AdminDashboard } from '@/components/admin/AdminDashboard'
import { isSystemAdmin } from '@/lib/roles'

export default async function DashboardPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  const isAdmin = profile && isSystemAdmin(profile.role)

  return (
    <DashboardLayout user={{
      name: profile?.full_name || user.email,
      email: user.email,
      role: profile?.role || 'sales_employee'
    }}>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold">
            {isAdmin ? 'مرحباً بك في لوحة الإدارة' : 'مرحباً بك'}
          </h2>
          <p className="text-muted-foreground mt-2">
            {isAdmin ? 'إدارة النظام والمستخدمين' : 'لوحة التحكم الخاصة بك في نظام سحابة المدينة'}
          </p>
        </div>

        {isAdmin ? (
          <AdminDashboard />
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>المهام</CardTitle>
                <CardDescription>
                  إدارة مهامك اليومية
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">0</p>
                <p className="text-sm text-muted-foreground">مهمة نشطة</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>المشاريع</CardTitle>
                <CardDescription>
                  تتبع تقدم مشاريعك
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">0</p>
                <p className="text-sm text-muted-foreground">مشروع جاري</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>التقارير</CardTitle>
                <CardDescription>
                  عرض التقارير والإحصائيات
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">0</p>
                <p className="text-sm text-muted-foreground">تقرير جديد</p>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}

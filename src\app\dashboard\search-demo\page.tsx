import { requireAuth, getUserProfile } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { SearchDemo } from '@/components/search/SearchDemo'

export default async function SearchDemoPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  if (!profile) {
    return <div>Loading...</div>
  }

  return (
    <DashboardLayout user={{
      name: profile.full_name || user.email,
      email: user.email,
      role: profile.role
    }}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">عرض توضيحي لنظام البحث</h1>
            <p className="text-muted-foreground mt-2">
              اختبر وظائف البحث المختلفة في النظام
            </p>
          </div>
        </div>

        <SearchDemo userRole={profile.role} />
      </div>
    </DashboardLayout>
  )
}

'use client'

import { useRouter } from 'next/navigation'
import { ProfileView } from './ProfileView'

interface ProfileViewWithEditProps {
  userId: string
}

export function ProfileViewWithEdit({ userId }: ProfileViewWithEditProps) {
  const router = useRouter()

  const handleEditClick = () => {
    router.push('/dashboard/profile/edit')
  }

  return (
    <ProfileView 
      userId={userId} 
      showEditButton={true}
      onEditClick={handleEditClick}
    />
  )
}

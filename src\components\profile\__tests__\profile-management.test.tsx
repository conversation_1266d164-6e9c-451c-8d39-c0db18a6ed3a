import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ProfileManagement } from '../profile-management'
import { useRouter } from 'next/navigation'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

// Mock Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({ data: mockProfile, error: null }))
          }))
        }))
      }))
    }))
  }))
}))

// Mock fetch
global.fetch = jest.fn()

const mockProfile = {
  id: '123',
  email: '<EMAIL>',
  full_name: 'اختبار المستخدم',
  phone: '123456789',
  role: 'sales_employee',
  role_level: 4,
  area_id: null,
  team_id: null,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  team: null,
  area: null
}

const mockRouter = {
  push: jest.fn(),
  refresh: jest.fn(),
}

describe('ProfileManagement', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockProfile)
    })
  })

  it('renders profile information correctly', () => {
    render(<ProfileManagement profile={mockProfile} />)
    
    expect(screen.getByText('اختبار المستخدم')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('123456789')).toBeInTheDocument()
  })

  it('enters edit mode when edit button is clicked', () => {
    render(<ProfileManagement profile={mockProfile} />)
    
    const editButton = screen.getByText('تعديل')
    fireEvent.click(editButton)
    
    expect(screen.getByDisplayValue('اختبار المستخدم')).toBeInTheDocument()
    expect(screen.getByDisplayValue('123456789')).toBeInTheDocument()
    expect(screen.getByText('حفظ')).toBeInTheDocument()
    expect(screen.getByText('إلغاء')).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    render(<ProfileManagement profile={mockProfile} />)
    
    const editButton = screen.getByText('تعديل')
    fireEvent.click(editButton)
    
    const nameInput = screen.getByDisplayValue('اختبار المستخدم')
    fireEvent.change(nameInput, { target: { value: '' } })
    
    const saveButton = screen.getByText('حفظ')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(screen.getByText('الاسم الكامل مطلوب')).toBeInTheDocument()
    })
  })

  it('saves profile changes successfully', async () => {
    render(<ProfileManagement profile={mockProfile} />)
    
    const editButton = screen.getByText('تعديل')
    fireEvent.click(editButton)
    
    const nameInput = screen.getByDisplayValue('اختبار المستخدم')
    fireEvent.change(nameInput, { target: { value: 'اسم جديد' } })
    
    const saveButton = screen.getByText('حفظ')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          full_name: 'اسم جديد',
          phone: '123456789'
        })
      })
    })
    
    await waitFor(() => {
      expect(screen.getByText('تم تحديث الملف الشخصي بنجاح')).toBeInTheDocument()
    })
  })

  it('cancels edit mode', () => {
    render(<ProfileManagement profile={mockProfile} />)
    
    const editButton = screen.getByText('تعديل')
    fireEvent.click(editButton)
    
    const nameInput = screen.getByDisplayValue('اختبار المستخدم')
    fireEvent.change(nameInput, { target: { value: 'اسم مختلف' } })
    
    const cancelButton = screen.getByText('إلغاء')
    fireEvent.click(cancelButton)
    
    // Should exit edit mode and revert changes
    expect(screen.getByText('تعديل')).toBeInTheDocument()
    expect(screen.queryByDisplayValue('اسم مختلف')).not.toBeInTheDocument()
  })
})

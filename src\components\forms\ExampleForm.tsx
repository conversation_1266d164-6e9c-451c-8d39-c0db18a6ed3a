'use client'

import { FormWrapper, SubmitButton } from '@/components/ui/form-wrapper'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useNavigation } from '@/components/providers/NavigationProvider'

interface ExampleFormProps {
  onSuccess?: () => void
}

export function ExampleForm({ onSuccess }: ExampleFormProps) {
  const { navigateWithLoading } = useNavigation()

  const handleSubmit = async (formData: FormData) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const title = formData.get('title') as string
    const description = formData.get('description') as string
    
    if (!title || !description) {
      throw new Error('يرجى ملء جميع الحقول المطلوبة')
    }
    
    // Simulate success
    return { success: true, data: { title, description } }
  }

  const handleSuccess = (result: any) => {
    console.log('Form submitted successfully:', result)
    onSuccess?.()
    // Navigate to another page after successful submission
    navigateWithLoading('/dashboard', 'جاري الانتقال إلى الرئيسية...')
  }

  const handleError = (error: any) => {
    console.error('Form submission error:', error)
  }

  return (
    <FormWrapper
      onSubmit={handleSubmit}
      loadingText="جاري إرسال البيانات..."
      successMessage="تم إرسال البيانات بنجاح!"
      onSuccess={handleSuccess}
      onError={handleError}
      className="max-w-md mx-auto"
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="title">العنوان *</Label>
          <Input
            id="title"
            name="title"
            type="text"
            placeholder="أدخل العنوان"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">الوصف *</Label>
          <Textarea
            id="description"
            name="description"
            placeholder="أدخل الوصف"
            rows={4}
            required
          />
        </div>

        <SubmitButton className="w-full">
          إرسال البيانات
        </SubmitButton>
      </div>
    </FormWrapper>
  )
}
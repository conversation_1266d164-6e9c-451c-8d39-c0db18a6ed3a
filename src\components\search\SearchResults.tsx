'use client'

import { SearchResult, SearchCategory } from '@/types/search'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Users,
  MapPin,
  MessageSquare,
  ShoppingCart,
  ClipboardCheck,
  Navigation,
  Search,
  Clock,
  User,
  Mail,
  Building,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import Link from 'next/link'

interface SearchResultsProps {
  categories: Record<string, SearchCategory>
  results: SearchResult[]
  loading: boolean
  error: string | null
  query: string
  selectedIndex: number
  onResultClick?: (result: SearchResult) => void
  className?: string
}

const iconMap = {
  Users,
  MapPin,
  MessageSquare,
  ShoppingCart,
  ClipboardCheck,
  Navigation,
  User,
  Building
}

const getIcon = (iconName: string) => {
  return iconMap[iconName as keyof typeof iconMap] || Search
}

const translateStatus = (status?: string) => {
  switch (status) {
    case 'open':
      return 'مفتوح'
    case 'in_progress':
      return 'قيد المعالجة'
    case 'closed':
      return 'مغلق'
    case 'نشط':
      return 'نشط'
    case 'غير نشط':
      return 'غير نشط'
    case 'مكتمل':
      return 'مكتمل'
    case 'غير مكتمل':
      return 'غير مكتمل'
    default:
      return status || 'غير محدد'
  }
}

const translatePriority = (priority?: string) => {
  switch (priority) {
    case 'high':
      return 'عالية'
    case 'medium':
      return 'متوسطة'
    case 'low':
      return 'منخفضة'
    default:
      return priority || 'غير محدد'
  }
}

const getStatusColor = (status?: string) => {
  switch (status) {
    case 'open':
    case 'مفتوح':
    case 'نشط':
      return 'bg-green-100 text-green-800'
    case 'in_progress':
    case 'قيد المعالجة':
      return 'bg-yellow-100 text-yellow-800'
    case 'closed':
    case 'مغلق':
    case 'غير نشط':
      return 'bg-gray-100 text-gray-800'
    case 'مكتمل':
      return 'bg-blue-100 text-blue-800'
    case 'غير مكتمل':
      return 'bg-orange-100 text-orange-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getPriorityColor = (priority?: string) => {
  switch (priority) {
    case 'high':
    case 'عالية':
      return 'bg-red-100 text-red-800'
    case 'medium':
    case 'متوسطة':
      return 'bg-yellow-100 text-yellow-800'
    case 'low':
    case 'منخفضة':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export function SearchResults({
  categories,
  results,
  loading,
  error,
  query,
  selectedIndex,
  onResultClick,
  className
}: SearchResultsProps) {
  if (loading) {
    return (
      <div className={cn("w-full h-16 border rounded-md bg-card text-card-foreground shadow-sm", className)} dir="rtl">
        <div className="p-4 h-full flex items-center justify-center">
          <div className="flex items-center gap-2 text-muted-foreground" dir="rtl">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-right">جاري البحث...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn("w-full h-16 border rounded-md bg-card text-card-foreground shadow-sm", className)} dir="rtl">
        <div className="p-4 h-full flex items-center justify-center">
          <div className="flex items-center gap-2 text-red-600" dir="rtl">
            <AlertCircle className="h-4 w-4" />
            <span className="text-right">{error}</span>
          </div>
        </div>
      </div>
    )
  }

  if (!query || results.length === 0) {
    return (
      <div className={cn("w-full h-16 border rounded-md bg-card text-card-foreground shadow-sm", className)} dir="rtl">
        <div className="p-4 h-full flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            {!query ? (
              <div className="flex items-center justify-center gap-2" dir="rtl">
                <Search className="h-4 w-4" />
                <span className="text-right">ابدأ بكتابة للبحث...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2" dir="rtl">
                <Search className="h-4 w-4" />
                <span className="text-right">لا توجد نتائج للبحث "{query}"</span>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  let currentIndex = 0

  return (
    <div className={cn("w-full max-h-64 border rounded-md bg-card text-card-foreground shadow-sm flex flex-col", className)} dir="rtl">
      <div className="flex-1 overflow-y-auto rtl-scrollbar">
        <div className="p-0">
          {Object.entries(categories).map(([categoryKey, category], categoryIndex) => (
            <div key={categoryKey}>
              {categoryIndex > 0 && <Separator />}
              
              {/* Category Header */}
              <div className="px-3 py-1.5 bg-muted/50" dir="rtl">
                <div className="flex items-center gap-2 text-xs font-medium justify-start">
                  {(() => {
                    const Icon = getIcon(category.icon)
                    return <Icon className="h-3 w-3" />
                  })()}
                  <span className="text-right">{category.arabicName}</span>
                  <Badge variant="secondary" className="text-xs mr-auto">
                    {category.results.length}
                  </Badge>
                </div>
              </div>

              {/* Category Results */}
              <div className="py-1">
                {category.results.slice(0, 5).map((result, resultIndex) => {
                  const isSelected = currentIndex === selectedIndex
                  const itemIndex = currentIndex++

                  return (
                    <Link
                      key={result.id}
                      href={result.url}
                      onClick={() => onResultClick?.(result)}
                      className={cn(
                        "block px-3 py-2 hover:bg-muted/50 transition-colors cursor-pointer",
                        isSelected && "bg-muted"
                      )}
                      dir="rtl"
                    >
                      <div className="space-y-1">
                        <div className="flex items-start justify-between gap-2">
                          <h4 className="text-sm font-medium text-right flex-1 truncate">
                            {result.title}
                          </h4>
                          <div className="flex items-center gap-1 flex-shrink-0">
                            {result.metadata?.status && (
                              <Badge
                                variant="secondary"
                                className={cn("text-xs", getStatusColor(result.metadata.status))}
                              >
                                {translateStatus(result.metadata.status)}
                              </Badge>
                            )}
                            {result.metadata?.priority && (
                              <Badge
                                variant="secondary"
                                className={cn("text-xs", getPriorityColor(result.metadata.priority))}
                              >
                                {translatePriority(result.metadata.priority)}
                              </Badge>
                            )}
                          </div>
                        </div>

                        {result.description && (
                          <p className="text-xs text-muted-foreground text-right leading-relaxed">
                            {result.description.length > 60 ? result.description.substring(0, 60) + '...' : result.description}
                          </p>
                        )}

                        {/* Metadata */}
                        <div className="flex items-center gap-2 text-xs text-muted-foreground justify-start flex-wrap">
                          {result.metadata?.email && (
                            <div className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              <span>{result.metadata.email}</span>
                            </div>
                          )}
                          {result.metadata?.area_name && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              <span>{result.metadata.area_name}</span>
                            </div>
                          )}
                          {result.metadata?.team_name && (
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              <span>{result.metadata.team_name}</span>
                            </div>
                          )}
                          {result.metadata?.created_at && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{new Date(result.metadata.created_at).toLocaleDateString('ar-SA')}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </Link>
                  )
                })}
              </div>
            </div>
          ))}

        </div>
      </div>

      {/* Fixed Footer */}
      <div className="px-3 py-1.5 bg-muted/30 border-t flex-shrink-0" dir="rtl">
        <div className="text-xs text-muted-foreground text-center">
          {results.length} نتيجة للبحث "{query}"
        </div>
      </div>
    </div>
  )
}

import type { Metada<PERSON> } from "next";
import { IBM_Plex_Sans_Arabic } from "next/font/google";
import { AuthProvider } from "@/components/providers/AuthProvider";
import { LoadingProvider } from "@/components/providers/LoadingProvider";
import { NavigationProvider } from "@/components/providers/NavigationProvider";
import { SessionRefresh } from "@/components/auth/SessionRefresh";
import { AuthDebug } from "@/components/debug/AuthDebug";
import { NavigationProgress } from "@/components/ui/navigation-progress";
import "./globals.css";

const ibmPlexArabic = IBM_Plex_Sans_Arabic({
  variable: "--font-ibm-plex-arabic",
  subsets: ["arabic"],
  weight: ["100", "200", "300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "سحابة المدينة",
  description: "نظام إدارة العمل المخصص للمدينة",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body
        className={`${ibmPlexArabic.variable} font-arabic antialiased`}
        suppressHydrationWarning={true}
      >
        <NavigationProgress />
        <LoadingProvider>
          <NavigationProvider>
            <AuthProvider>
              <SessionRefresh />
              {children}
              <AuthDebug />
            </AuthProvider>
          </NavigationProvider>
        </LoadingProvider>
      </body>
    </html>
  );
}

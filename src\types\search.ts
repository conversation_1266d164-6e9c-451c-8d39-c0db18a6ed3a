export interface SearchResult {
  id: string
  title: string
  description?: string
  type: 'user' | 'area' | 'team' | 'ticket' | 'package' | 'navigation' | 'daily_closing'
  url: string
  metadata?: {
    role?: string
    status?: string
    priority?: string
    area_name?: string
    team_name?: string
    email?: string
    created_at?: string
    updated_at?: string
  }
}

export interface SearchCategory {
  name: string
  arabicName: string
  results: SearchResult[]
  icon: string
}

export interface SearchResponse {
  results: SearchResult[]
  categories: Record<string, SearchCategory>
  total: number
  query: string
}

export interface SearchFilters {
  type?: string[]
  role?: string[]
  status?: string[]
  priority?: string[]
}

export interface SearchOptions {
  query: string
  filters?: SearchFilters
  limit?: number
  offset?: number
}

export type SearchType = 
  | 'user' 
  | 'area' 
  | 'team' 
  | 'ticket' 
  | 'package' 
  | 'navigation' 
  | 'daily_closing'
  | 'all'

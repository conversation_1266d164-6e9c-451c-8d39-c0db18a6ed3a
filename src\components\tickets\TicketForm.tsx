'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FormWrapper, SubmitButton } from '@/components/ui/form-wrapper'
import { useNavigation } from '@/components/providers/NavigationProvider'
import { ArrowRight, Send } from 'lucide-react'
import { createTicket } from '@/app/dashboard/tickets/actions'

interface TicketFormProps {
  userId: string
}

export function TicketForm({ userId }: TicketFormProps) {
  const router = useRouter()
  const { navigateWithLoading } = useNavigation()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium' as 'low' | 'medium' | 'high'
  })

  const handleSubmit = async (formDataObj: FormData) => {
    // Add form state to FormData
    formDataObj.set('title', formData.title.trim())
    formDataObj.set('description', formData.description.trim())
    formDataObj.set('priority', formData.priority)

    if (!formData.title.trim() || !formData.description.trim()) {
      throw new Error('يرجى ملء جميع الحقول المطلوبة')
    }

    const result = await createTicket(formDataObj)

    if (result?.error) {
      throw new Error(result.error)
    }

    return result
  }

  const handleSuccess = (result: any) => {
    console.log('Ticket created successfully:', result)
    // Navigate back to tickets list with loading indicator
    navigateWithLoading('/dashboard/tickets', 'جاري الانتقال إلى قائمة الطلبات...')
  }

  const handleCancel = () => {
    navigateWithLoading('/dashboard/tickets', 'جاري العودة إلى قائمة الطلبات...')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowRight className="h-4 w-4" />
          العودة للطلبات
        </Button>
      </div>

      <div>
        <h2 className="text-3xl font-bold">طلب جديد</h2>
        <p className="text-muted-foreground mt-2">
          أنشئ طلباً جديداً لفريق الإدارة
        </p>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>تفاصيل الطلب</CardTitle>
          <CardDescription>
            املأ النموذج أدناه لإنشاء طلب جديد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FormWrapper
            onSubmit={handleSubmit}
            loadingText="جاري إنشاء الطلب..."
            successMessage="تم إنشاء الطلب بنجاح!"
            onSuccess={handleSuccess}
            className="space-y-6"
          >
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">عنوان الطلب *</Label>
              <Input
                id="title"
                name="title"
                type="text"
                placeholder="أدخل عنوان الطلب"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                required
                className="text-right"
              />
            </div>

            {/* Priority */}
            <div className="space-y-2">
              <Label htmlFor="priority">الأولوية</Label>
              <Select
                name="priority"
                value={formData.priority}
                onValueChange={(value: 'low' | 'medium' | 'high') => 
                  setFormData(prev => ({ ...prev, priority: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر الأولوية" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">منخفضة</SelectItem>
                  <SelectItem value="medium">متوسطة</SelectItem>
                  <SelectItem value="high">عالية</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">وصف الطلب *</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="اشرح طلبك بالتفصيل..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                required
                rows={6}
                className="text-right resize-none"
              />
            </div>

            {/* Actions */}
            <div className="flex items-center gap-3 pt-4">
              <SubmitButton
                className="flex items-center gap-2"
                disabled={!formData.title.trim() || !formData.description.trim()}
              >
                <Send className="h-4 w-4" />
                إرسال الطلب
              </SubmitButton>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
              >
                إلغاء
              </Button>
            </div>
          </FormWrapper>
        </CardContent>
      </Card>
    </div>
  )
}

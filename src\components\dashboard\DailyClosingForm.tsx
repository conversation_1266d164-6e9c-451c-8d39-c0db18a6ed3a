'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Clock, Calendar, MapPin, Users } from 'lucide-react'
import { AttendanceSection } from './daily-closing/AttendanceSection'
import { SalesSection } from './daily-closing/SalesSection'
import { DepartureSection } from './daily-closing/DepartureSection'
import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/supabase'

type Profile = Database['public']['Tables']['profiles']['Row']
type DailyClosing = Database['public']['Tables']['daily_closings']['Row']
type Area = Database['public']['Tables']['areas']['Row']
type Team = Database['public']['Tables']['teams']['Row']

interface DailyClosingFormProps {
  user: Profile | null
}

export function DailyClosingForm({ user }: DailyClosingFormProps) {
  const [dailyClosing, setDailyClosing] = useState<DailyClosing | null>(null)
  const [area, setArea] = useState<Area | null>(null)
  const [team, setTeam] = useState<Team | null>(null)
  const [teamLeader, setTeamLeader] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  const [currentDateTime, setCurrentDateTime] = useState({
    today: '',
    currentTime: '',
    currentDate: ''
  })

  // Initialize date/time on client side to avoid hydration mismatch
  useEffect(() => {
    const now = new Date()
    setCurrentDateTime({
      today: now.toISOString().split('T')[0],
      currentTime: now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        timeZone: 'UTC'
      }),
      currentDate: now.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'UTC'
      })
    })
  }, [])

  useEffect(() => {
    if (user && user.id && currentDateTime.today) {
      console.log('User loaded, fetching daily closing data:', user)
      fetchDailyClosingData()
      fetchUserAreaAndTeam()
    } else if (user && user.id && !currentDateTime.today) {
      // Wait for currentDateTime to be initialized
      console.log('Waiting for currentDateTime to be initialized')
    } else {
      console.log('No valid user provided, setting loading to false')
      setLoading(false)
      if (!user) {
        setError('لم يتم العثور على بيانات المستخدم. يرجى تسجيل الدخول.')
      } else if (!user.id) {
        setError('بيانات المستخدم غير مكتملة. يرجى تسجيل الدخول مرة أخرى.')
      }
    }
  }, [user, currentDateTime.today])

  const fetchDailyClosingData = async () => {
    if (!user) {
      console.log('No user provided to fetchDailyClosingData')
      setError('لم يتم العثور على بيانات المستخدم')
      setLoading(false)
      return
    }

    try {
      console.log('Fetching daily closing for user:', user.id, 'date:', currentDateTime.today)

      // Get or create today's daily closing record
      let { data: existingClosing, error } = await supabase
        .from('daily_closings')
        .select('*')
        .eq('user_id', user.id)
        .eq('closing_date', currentDateTime.today)
        .single()

      console.log('Daily closing query result:', { existingClosing, error })

      if (error && error.code === 'PGRST116') {
        console.log('No existing record found, creating new one')
        // Create new daily closing record
        const { data: newClosing, error: createError } = await supabase
          .from('daily_closings')
          .insert({
            user_id: user.id,
            closing_date: currentDateTime.today
          })
          .select()
          .single()

        console.log('Create daily closing result:', { newClosing, createError })

        if (createError) {
          console.error('Error creating daily closing:', createError)
          const errorMessage = createError.message || createError.details || 'خطأ غير معروف'
          setError('فشل في إنشاء سجل التقفيل اليومي: ' + errorMessage)
          setLoading(false)
          return
        }

        existingClosing = newClosing
      } else if (error) {
        console.error('Error fetching daily closing:', error)
        const errorMessage = error.message || error.details || 'خطأ غير معروف'
        setError('فشل في تحميل بيانات التقفيل اليومي: ' + errorMessage)
        setLoading(false)
        return
      }

      setDailyClosing(existingClosing)
    } catch (error: any) {
      console.error('Error in fetchDailyClosingData:', error)
      setError('حدث خطأ غير متوقع: ' + (error.message || 'خطأ غير معروف'))
    } finally {
      setLoading(false)
    }
  }

  const fetchUserAreaAndTeam = async () => {
    if (!user?.area_id && !user?.team_id) {
      console.log('User has no area_id or team_id, skipping area/team fetch')
      return
    }

    try {
      console.log('Fetching area/team data for user:', { area_id: user.area_id, team_id: user.team_id })

      // Fetch area info
      if (user.area_id) {
        const { data: areaData, error: areaError } = await supabase
          .from('areas')
          .select('*')
          .eq('id', user.area_id)
          .single()

        console.log('Area fetch result:', { areaData, areaError })
        if (areaError) {
          console.warn('Could not fetch area data:', areaError.message || 'Unknown error')
        } else if (areaData) {
          setArea(areaData)
        }
      }

      // Fetch team info and team leader
      if (user.team_id) {
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .select('*')
          .eq('id', user.team_id)
          .single()

        console.log('Team fetch result:', { teamData, teamError })
        if (teamError) {
          console.warn('Could not fetch team data:', teamError.message || 'Unknown error')
        } else if (teamData) {
          setTeam(teamData)

          // Fetch team leader info
          if (teamData.manager_id) {
            try {
              const { data: leaderData, error: leaderError } = await supabase
                .from('profiles')
                .select('id, full_name, email, role')
                .eq('id', teamData.manager_id)
                .single()

              console.log('Team leader fetch result:', { leaderData, leaderError })
              if (leaderError) {
                // Only log error if it's not a "no rows" error
                if (leaderError.code !== 'PGRST116') {
                  console.warn('Could not fetch team leader data:', leaderError.message || 'Unknown error')
                } else {
                  console.log('No team leader profile found for manager_id:', teamData.manager_id)
                }
              } else if (leaderData) {
                setTeamLeader(leaderData)
              }
            } catch (error: any) {
              console.warn('Exception while fetching team leader:', error.message || 'Unknown error')
            }
          }
        }
      }
    } catch (error) {
      console.error('Error fetching area/team data:', error)
      // Don't set error state here as it's not critical for the main functionality
      // The UI will show "غير محدد" for missing data with refresh buttons
    }
  }

  const refreshDailyClosing = async () => {
    setError(null)
    setLoading(true)
    await fetchDailyClosingData()
    // Also refresh area and team data in case it changed
    await fetchUserAreaAndTeam()
  }

  const retryDataLoad = async () => {
    setError(null)
    setLoading(true)
    if (user) {
      await fetchDailyClosingData()
      await fetchUserAreaAndTeam()
    } else {
      setError('لم يتم العثور على بيانات المستخدم')
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center p-8">
          <div className="space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-primary border-t-transparent mx-auto"></div>
            <div className="space-y-2">
              <p className="text-lg font-medium">جاري تحميل التقفيل اليومي...</p>
              {user && (
                <p className="text-sm text-muted-foreground">
                  تحميل بيانات: {user.full_name || user.email}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-destructive mb-4">{error}</p>
        <button
          onClick={retryDataLoad}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
        >
          إعادة المحاولة
        </button>
      </div>
    )
  }

  if (!dailyClosing) {
    return (
      <div className="text-center p-8">
        <p className="text-destructive">حدث خطأ في تحميل بيانات التقفيل اليومي</p>
        <button
          onClick={retryDataLoad}
          className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
        >
          إعادة المحاولة
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Comprehensive Info & Progress Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            التقفيل اليومي - {currentDateTime.currentDate}
          </CardTitle>
          <CardDescription>
            تتبع تقدمك في إكمال التقفيل اليومي وعرض المعلومات الأساسية
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Progress Indicators */}
          <div>
            <h4 className="font-medium mb-3">حالة التقفيل</h4>
            <div className="flex items-center gap-4 flex-wrap">
              <Badge
                variant={dailyClosing.attendance_submitted ? "default" : "secondary"}
                className="text-sm px-3 py-1"
              >
                {dailyClosing.attendance_submitted ? "✓" : "1"} تسجيل الحضور
              </Badge>
              <Badge
                variant={dailyClosing.sales_submitted ? "default" : "secondary"}
                className="text-sm px-3 py-1"
              >
                {dailyClosing.sales_submitted ? "✓" : "2"} تسجيل المبيعات
              </Badge>
              <Badge
                variant={dailyClosing.departure_submitted ? "default" : "secondary"}
                className="text-sm px-3 py-1"
              >
                {dailyClosing.departure_submitted ? "✓" : "3"} تسجيل الانصراف
              </Badge>
            </div>
          </div>

          <Separator />

          {/* Basic Information */}
          <div>
            <h4 className="font-medium mb-3">المعلومات الأساسية</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">التاريخ</p>
                  <p className="font-medium">{currentDateTime.currentDate}</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">الوقت الحالي</p>
                  <p className="font-medium">{currentDateTime.currentTime}</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">المنطقة</p>
                  <p className="font-medium">{area?.name || 'غير محدد'}</p>
                  {!area && user?.area_id && (
                    <button
                      onClick={fetchUserAreaAndTeam}
                      className="text-xs text-blue-600 hover:underline"
                    >
                      تحديث
                    </button>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">قائد الفريق</p>
                  <p className="font-medium">{teamLeader?.full_name || teamLeader?.email || 'غير محدد'}</p>
                  {!teamLeader && user?.team_id && (
                    <button
                      onClick={fetchUserAreaAndTeam}
                      className="text-xs text-blue-600 hover:underline"
                    >
                      تحديث
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Sales Summary (if sales submitted) */}
          {dailyClosing.sales_submitted && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium mb-3">ملخص المبيعات</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                    <p className="text-sm text-green-700">إجمالي المبيعات</p>
                    <p className="text-lg font-bold text-green-600">{dailyClosing.total_sales_amount.toFixed(2)} ر.س</p>
                  </div>
                  <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                    <p className="text-sm text-blue-700">المبلغ المسلم</p>
                    <p className="text-lg font-bold text-blue-600">{dailyClosing.cash_delivered.toFixed(2)} ر.س</p>
                  </div>
                  {(dailyClosing.advances_amount || 0) > 0 && (
                    <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                      <p className="text-sm text-purple-700">السلف</p>
                      <p className="text-lg font-bold text-purple-600">-{(dailyClosing.advances_amount || 0).toFixed(2)} ر.س</p>
                    </div>
                  )}
                  {(dailyClosing.price_breaks_amount || 0) > 0 && (
                    <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                      <p className="text-sm text-orange-700">كسر السعر</p>
                      <p className="text-lg font-bold text-orange-600">-{(dailyClosing.price_breaks_amount || 0).toFixed(2)} ر.س</p>
                    </div>
                  )}
                  {dailyClosing.deficit_amount > 0 ? (
                    <div className="bg-amber-50 p-3 rounded-lg border border-amber-200">
                      <p className="text-sm text-amber-700">العجز النهائي</p>
                      <p className="text-lg font-bold text-amber-600">{dailyClosing.deficit_amount.toFixed(2)} ر.س</p>
                    </div>
                  ) : (
                    <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                      <p className="text-sm text-green-700">لا يوجد عجز</p>
                      <p className="text-lg font-bold text-green-600">0.00 ر.س</p>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Completion Status */}
          {dailyClosing.attendance_submitted && dailyClosing.sales_submitted && dailyClosing.departure_submitted && (
            <>
              <Separator />
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 text-green-700">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="font-medium">تم إكمال التقفيل اليومي بنجاح</span>
                </div>
                <p className="text-sm text-green-600 mt-1">
                  تم تسجيل جميع البيانات المطلوبة لليوم
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Section 1: Attendance */}
      <AttendanceSection 
        dailyClosing={dailyClosing}
        onUpdate={refreshDailyClosing}
      />

      <Separator />

      {/* Section 2: Sales */}
      <SalesSection 
        dailyClosing={dailyClosing}
        onUpdate={refreshDailyClosing}
        disabled={!dailyClosing.attendance_submitted}
      />

      <Separator />

      {/* Section 3: Departure */}
      <DepartureSection 
        dailyClosing={dailyClosing}
        onUpdate={refreshDailyClosing}
        disabled={!dailyClosing.sales_submitted}
      />
    </div>
  )
}

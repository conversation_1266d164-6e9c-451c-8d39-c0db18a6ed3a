-- Update role system to support 4-tier hierarchy
-- مدير النظام (system_admin) - Level 1 (Highest)
-- مدير منطقة (area_manager) - Level 2  
-- مدير فريق (team_manager) - Level 3
-- موظف مبيعات (sales_employee) - Level 4 (Lowest)

-- First, add a temporary column for the new role system
ALTER TABLE public.profiles 
ADD COLUMN new_role TEXT;

-- Add role hierarchy level for easier permission checking
ALTER TABLE public.profiles 
ADD COLUMN role_level INTEGER;

-- Update existing admin users to system_admin
UPDATE public.profiles 
SET new_role = 'system_admin', role_level = 1
WHERE role = 'admin';

-- Update existing regular users to sales_employee (lowest level)
UPDATE public.profiles 
SET new_role = 'sales_employee', role_level = 4
WHERE role = 'user';

-- Drop the old role column
ALTER TABLE public.profiles 
DROP COLUMN role;

-- Rename new_role to role
ALTER TABLE public.profiles 
RENAME COLUMN new_role TO role;

-- Add constraint for valid roles
ALTER TABLE public.profiles 
ADD CONSTRAINT valid_roles 
CHECK (role IN ('system_admin', 'area_manager', 'team_manager', 'sales_employee'));

-- Add constraint for valid role levels
ALTER TABLE public.profiles 
ADD CONSTRAINT valid_role_levels 
CHECK (role_level IN (1, 2, 3, 4));

-- Add constraint to ensure role and role_level match
ALTER TABLE public.profiles 
ADD CONSTRAINT role_level_consistency 
CHECK (
  (role = 'system_admin' AND role_level = 1) OR
  (role = 'area_manager' AND role_level = 2) OR
  (role = 'team_manager' AND role_level = 3) OR
  (role = 'sales_employee' AND role_level = 4)
);

-- Set default role to sales_employee (lowest privilege)
ALTER TABLE public.profiles 
ALTER COLUMN role SET DEFAULT 'sales_employee';

-- Set default role_level to 4 (lowest privilege)
ALTER TABLE public.profiles 
ALTER COLUMN role_level SET DEFAULT 4;

-- Make role and role_level NOT NULL
ALTER TABLE public.profiles 
ALTER COLUMN role SET NOT NULL;

ALTER TABLE public.profiles 
ALTER COLUMN role_level SET NOT NULL;

-- Update the handle_new_user function to set default role and level
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, phone, role, role_level)
    VALUES (
        NEW.id, 
        NEW.email, 
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'phone',
        'sales_employee',
        4
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies to work with the new role system

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Admins can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Admins can delete profiles" ON public.profiles;

-- Create new policies based on role hierarchy

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile (except role and role_level)
CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id AND 
        role = (SELECT role FROM public.profiles WHERE id = auth.uid()) AND
        role_level = (SELECT role_level FROM public.profiles WHERE id = auth.uid())
    );

-- System admins can view all profiles
CREATE POLICY "System admins can view all profiles" ON public.profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- System admins can insert new profiles
CREATE POLICY "System admins can insert profiles" ON public.profiles
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- System admins can update all profiles
CREATE POLICY "System admins can update all profiles" ON public.profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- System admins can delete profiles (except their own)
CREATE POLICY "System admins can delete profiles" ON public.profiles
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'system_admin'
        ) AND id != auth.uid()
    );

-- Area managers can view profiles of lower hierarchy levels
CREATE POLICY "Area managers can view subordinates" ON public.profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'area_manager'
        ) AND role_level >= 2
    );

-- Team managers can view profiles of lower hierarchy levels  
CREATE POLICY "Team managers can view subordinates" ON public.profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'team_manager'
        ) AND role_level >= 3
    );

-- Create helper function to check if user has higher or equal role level
CREATE OR REPLACE FUNCTION public.has_role_level_or_higher(required_level INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role_level <= required_level
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to get user role level
CREATE OR REPLACE FUNCTION public.get_user_role_level()
RETURNS INTEGER AS $$
DECLARE
    user_level INTEGER;
BEGIN
    SELECT role_level INTO user_level
    FROM public.profiles 
    WHERE id = auth.uid();
    
    RETURN COALESCE(user_level, 5); -- Return 5 (lower than any valid level) if not found
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to get user role
CREATE OR REPLACE FUNCTION public.get_user_role()
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM public.profiles 
    WHERE id = auth.uid();
    
    RETURN user_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

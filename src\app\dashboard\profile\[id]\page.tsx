import { redirect } from 'next/navigation'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ProfileView } from '@/components/profile/ProfileView'

interface ProfilePageProps {
  params: Promise<{
    id: string
  }>
}

export default async function ProfilePage({ params }: ProfilePageProps) {
  const { id } = await params
  const cookieStore = await cookies()
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get current user profile
  const { data: currentUserProfile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!currentUserProfile) {
    redirect('/login')
  }

  // Get target user profile
  const { data: targetProfile } = await supabase
    .from('profiles')
    .select(`
      *,
      team:teams!profiles_team_id_fkey(name, area:areas!teams_area_id_fkey(name)),
      area:areas!profiles_area_id_fkey(name)
    `)
    .eq('id', id)
    .single()

  if (!targetProfile) {
    redirect('/dashboard')
  }

  return (
    <DashboardLayout user={{
      name: currentUserProfile.full_name || user.email,
      email: currentUserProfile.email,
      role: currentUserProfile.role
    }}>
      <ProfileView userId={id} />
    </DashboardLayout>
  )
}

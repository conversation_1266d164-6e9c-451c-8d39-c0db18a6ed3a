'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'

import { UserCheck, Users, MapPin, Building, Search } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { UserRole } from '@/lib/supabase'
import { getRoleArabicName, getRoleBadgeVariant } from '@/lib/roles'

type User = {
  id: string
  full_name: string | null
  email: string
  role: UserRole
  role_level: number
  area_id: string | null
  team_id: string | null
  area?: {
    id: string
    name: string
  } | null
  team?: {
    id: string
    name: string
  } | null
}

type Area = {
  id: string
  name: string
}

type Team = {
  id: string
  name: string
  area_id: string
}

export function UserAssignment() {
  const [users, setUsers] = useState<User[]>([])
  const [areas, setAreas] = useState<Area[]>([])
  const [teams, setTeams] = useState<Team[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  // Filter states
  const [userFilter, setUserFilter] = useState('all')
  const [selectedAreaFilter, setSelectedAreaFilter] = useState('')
  const [selectedTeamFilter, setSelectedTeamFilter] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  
  // Assignment states
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [assignmentArea, setAssignmentArea] = useState('')
  const [assignmentTeam, setAssignmentTeam] = useState('')
  const [assignmentLoading, setAssignmentLoading] = useState(false)

  useEffect(() => {
    fetchUsers()
    fetchAreas()
    fetchTeams()
  }, [])

  useEffect(() => {
    // Filter teams by selected area
    if (selectedAreaFilter) {
      fetchTeams(selectedAreaFilter)
    } else {
      fetchTeams()
    }
  }, [selectedAreaFilter])

  const fetchUsers = async (filter?: string) => {
    try {
      const url = filter ? `/api/admin/assign-users?filter=${filter}` : '/api/admin/assign-users'
      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في جلب المستخدمين')
      }

      setUsers(data.users || [])
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const fetchAreas = async () => {
    try {
      const response = await fetch('/api/admin/areas')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في جلب المناطق')
      }

      setAreas(data.areas || [])
    } catch (err: any) {
      console.error('Error fetching areas:', err)
    }
  }

  const fetchTeams = async (areaId?: string) => {
    try {
      const url = areaId ? `/api/admin/teams?area_id=${areaId}` : '/api/admin/teams'
      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في جلب الفرق')
      }

      setTeams(data.teams || [])
    } catch (err: any) {
      console.error('Error fetching teams:', err)
    }
  }

  const handleFilterChange = (filter: string) => {
    setUserFilter(filter)
    setLoading(true)
    setSelectedUsers([])
    fetchUsers(filter === 'all' ? undefined : filter)
  }

  const handleAreaFilterChange = (areaId: string) => {
    setSelectedAreaFilter(areaId)
    setSelectedTeamFilter('')
    setLoading(true)
    
    if (areaId) {
      fetchUsers('by_area')
    } else {
      fetchUsers(userFilter === 'all' ? undefined : userFilter)
    }
  }

  const handleUserSelection = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId])
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Only select users who are not system admins
      const selectableUsers = users.filter(user => user.role !== 'system_admin')
      setSelectedUsers(selectableUsers.map(user => user.id))
    } else {
      setSelectedUsers([])
    }
  }

  const handleAssignment = async (action: 'assign_area' | 'assign_team' | 'remove_assignments') => {
    if (selectedUsers.length === 0) {
      setError('يرجى اختيار مستخدم واحد على الأقل')
      return
    }

    if (action === 'assign_area' && !assignmentArea) {
      setError('يرجى اختيار المنطقة')
      return
    }

    if (action === 'assign_team' && (!assignmentArea || !assignmentTeam)) {
      setError('يرجى اختيار المنطقة والفريق')
      return
    }

    setAssignmentLoading(true)
    setError('')

    try {
      const response = await fetch('/api/admin/assign-users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ids: selectedUsers,
          area_id: assignmentArea || null,
          team_id: assignmentTeam || null,
          action
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في تعيين المستخدمين')
      }

      setSuccess(data.message || 'تم تحديث التعيينات بنجاح')
      setSelectedUsers([])
      setAssignmentArea('')
      setAssignmentTeam('')
      
      // Refresh users list
      fetchUsers(userFilter === 'all' ? undefined : userFilter)
      
    } catch (err: any) {
      setError(err.message)
    } finally {
      setAssignmentLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">جاري تحميل المستخدمين...</p>
        </div>
      </div>
    )
  }

  const filteredUsers = users.filter(user => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      const matchesName = user.full_name?.toLowerCase().includes(query)
      const matchesEmail = user.email.toLowerCase().includes(query)
      if (!matchesName && !matchesEmail) return false
    }

    // Type filter
    switch (userFilter) {
      case 'unassigned':
        if (user.area_id || user.team_id) return false
        break
      case 'area_only':
        if (!user.area_id || user.team_id) return false
        break
      case 'with_team':
        if (!user.team_id) return false
        break
      case 'by_area':
        if (selectedAreaFilter && user.area_id !== selectedAreaFilter) return false
        break
    }

    // Additional filters
    if (selectedTeamFilter && user.team_id !== selectedTeamFilter) return false

    return true
  })

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold">تعيين المستخدمين</h2>
        <p className="text-muted-foreground">تعيين المستخدمين للمناطق والفرق</p>
      </div>



      {/* Main Content */}
      <UserAssignmentContent
        users={filteredUsers}
        areas={areas}
        teams={userFilter === 'by_area' ? teams.filter(team => team.area_id === selectedAreaFilter) : teams}
        selectedUsers={selectedUsers}
        assignmentArea={assignmentArea}
        assignmentTeam={assignmentTeam}
        assignmentLoading={assignmentLoading}
        error={error}
        success={success}
        searchQuery={searchQuery}
        userFilter={userFilter}
        selectedAreaFilter={selectedAreaFilter}
        onUserSelection={handleUserSelection}
        onSelectAll={handleSelectAll}
        onAssignmentAreaChange={setAssignmentArea}
        onAssignmentTeamChange={setAssignmentTeam}
        onAssignment={handleAssignment}
        onSearchChange={setSearchQuery}
        onFilterChange={handleFilterChange}
        onAreaFilterChange={handleAreaFilterChange}
      />
    </div>
  )
}

interface UserAssignmentContentProps {
  users: User[]
  areas: Area[]
  teams: Team[]
  selectedUsers: string[]
  assignmentArea: string
  assignmentTeam: string
  assignmentLoading: boolean
  error: string
  success: string
  searchQuery: string
  userFilter: string
  selectedAreaFilter: string
  onUserSelection: (userId: string, checked: boolean) => void
  onSelectAll: (checked: boolean) => void
  onAssignmentAreaChange: (areaId: string) => void
  onAssignmentTeamChange: (teamId: string) => void
  onAssignment: (action: 'assign_area' | 'assign_team' | 'remove_assignments') => void
  onSearchChange: (query: string) => void
  onFilterChange: (filter: string) => void
  onAreaFilterChange: (areaId: string) => void
}

function UserAssignmentContent({
  users,
  areas,
  teams,
  selectedUsers,
  assignmentArea,
  assignmentTeam,
  assignmentLoading,
  error,
  success,
  searchQuery,
  userFilter,
  selectedAreaFilter,
  onUserSelection,
  onSelectAll,
  onAssignmentAreaChange,
  onAssignmentTeamChange,
  onAssignment,
  onSearchChange,
  onFilterChange,
  onAreaFilterChange
}: UserAssignmentContentProps) {
  const filteredTeams = teams.filter(team => team.area_id === assignmentArea)

  return (
    <div className="space-y-4">
      {/* Assignment Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">تعيين المستخدمين المحددين</CardTitle>
          <CardDescription>
            اختر المستخدمين من القائمة أدناه ثم حدد المنطقة أو الفريق للتعيين
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="grid gap-2">
              <Label htmlFor="assignment-area">المنطقة</Label>
              <Select value={assignmentArea} onValueChange={onAssignmentAreaChange}>
                <SelectTrigger className="text-right" dir="rtl">
                  <SelectValue placeholder="اختر المنطقة" />
                </SelectTrigger>
                <SelectContent>
                  {areas.map((area) => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="assignment-team">الفريق</Label>
              <Select 
                value={assignmentTeam} 
                onValueChange={onAssignmentTeamChange}
                disabled={!assignmentArea}
              >
                <SelectTrigger className="text-right" dir="rtl">
                  <SelectValue placeholder="اختر الفريق" />
                </SelectTrigger>
                <SelectContent>
                  {filteredTeams.map((team) => (
                    <SelectItem key={team.id} value={team.id}>
                      {team.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={() => onAssignment('assign_area')}
              disabled={assignmentLoading || selectedUsers.length === 0 || !assignmentArea}
              className="cursor-pointer"
            >
              <MapPin className="h-4 w-4 ml-2" />
              تعيين للمنطقة فقط
            </Button>
            
            <Button
              onClick={() => onAssignment('assign_team')}
              disabled={assignmentLoading || selectedUsers.length === 0 || !assignmentArea || !assignmentTeam}
              className="cursor-pointer"
            >
              <Building className="h-4 w-4 ml-2" />
              تعيين للفريق
            </Button>
            
            <Button
              variant="outline"
              onClick={() => onAssignment('remove_assignments')}
              disabled={assignmentLoading || selectedUsers.length === 0}
              className="cursor-pointer"
            >
              إلغاء التعيين
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            المستخدمين المحددين: {selectedUsers.length}
          </div>
        </CardContent>
      </Card>

      {/* Success/Error Messages */}
      {success && (
        <Alert className="border-green-200 bg-green-50 text-green-800">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Users List */}
      <Card>
        <CardHeader>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">قائمة المستخدمين</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  ملاحظة: مديري النظام لا يمكن تعيينهم للمناطق أو الفرق
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={
                    users.filter(user => user.role !== 'system_admin').length > 0 &&
                    selectedUsers.length === users.filter(user => user.role !== 'system_admin').length
                  }
                  onCheckedChange={onSelectAll}
                  className="cursor-pointer"
                />
                <Label htmlFor="select-all" className="cursor-pointer">تحديد الكل</Label>
              </div>
            </div>

            {/* Search and Filter */}
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <Label htmlFor="search">البحث في المستخدمين</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    type="text"
                    placeholder="ابحث بالاسم أو البريد الإلكتروني..."
                    value={searchQuery}
                    onChange={(e) => onSearchChange(e.target.value)}
                    className="pl-10 text-right"
                    dir="rtl"
                  />
                </div>
              </div>

              <div className="flex-1">
                <Label htmlFor="user-filter">نوع التصفية</Label>
                <Select value={userFilter} onValueChange={onFilterChange}>
                  <SelectTrigger className="text-right" dir="rtl">
                    <SelectValue placeholder="اختر نوع التصفية" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع المستخدمين</SelectItem>
                    <SelectItem value="unassigned">غير معينين</SelectItem>
                    <SelectItem value="area_only">منطقة فقط</SelectItem>
                    <SelectItem value="with_team">مع فريق</SelectItem>
                    <SelectItem value="by_area">حسب المنطقة</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {userFilter === 'by_area' && (
                <div className="flex-1">
                  <Label htmlFor="area-filter">اختر المنطقة</Label>
                  <Select value={selectedAreaFilter} onValueChange={onAreaFilterChange}>
                    <SelectTrigger className="text-right" dir="rtl">
                      <SelectValue placeholder="اختر المنطقة" />
                    </SelectTrigger>
                    <SelectContent>
                      {areas.map((area) => (
                        <SelectItem key={area.id} value={area.id}>
                          {area.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {users.map((user) => {
              const isSystemAdmin = user.role === 'system_admin'
              return (
                <div key={user.id} className={`flex items-center justify-between p-3 border rounded-lg ${isSystemAdmin ? 'bg-muted/50 opacity-60' : ''}`}>
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id={`user-${user.id}`}
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => onUserSelection(user.id, checked as boolean)}
                      className="cursor-pointer"
                      disabled={isSystemAdmin}
                    />
                    <div className="flex items-center gap-3">
                      <div>
                        <p className={`font-medium ${isSystemAdmin ? 'text-muted-foreground' : ''}`}>
                          {user.full_name || user.email}
                          {isSystemAdmin && <span className="text-xs text-orange-600 mr-2">(لا يمكن تعيينه)</span>}
                        </p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                      <Badge variant={getRoleBadgeVariant(user.role)}>
                        {getRoleArabicName(user.role)}
                      </Badge>
                    </div>
                  </div>
                
                <div className="text-right text-sm">
                  {user.area && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      <span>{user.area.name}</span>
                    </div>
                  )}
                  {user.team && (
                    <div className="flex items-center gap-1">
                      <Building className="h-3 w-3" />
                      <span>{user.team.name}</span>
                    </div>
                  )}
                  {!user.area && !user.team && (
                    <span className="text-muted-foreground">غير معين</span>
                  )}
                </div>
              </div>
              )
            })}
          </div>

          {users.length === 0 && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">لا توجد مستخدمين</h3>
              <p className="text-muted-foreground">
                {searchQuery
                  ? `لا توجد نتائج للبحث "${searchQuery}"`
                  : userFilter === 'by_area' && !selectedAreaFilter
                    ? 'يرجى اختيار منطقة لعرض المستخدمين'
                    : 'لا توجد مستخدمين يطابقون المرشح المحدد'
                }
              </p>
              {searchQuery && (
                <Button
                  variant="outline"
                  onClick={() => onSearchChange('')}
                  className="mt-4 cursor-pointer"
                >
                  مسح البحث
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

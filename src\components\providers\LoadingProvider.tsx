'use client'

import React, { createContext, useContext, useState, useCallback } from 'react'
import { LoadingPage } from '@/components/ui/loading'

interface LoadingContextType {
  isLoading: boolean
  loadingText: string
  showLoading: (text?: string) => void
  hideLoading: () => void
  withLoading: <T>(promise: Promise<T>, text?: string) => Promise<T>
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export function LoadingProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(false)
  const [loadingText, setLoadingText] = useState('')

  const showLoading = useCallback((text?: string) => {
    setLoadingText(text || 'جاري التحميل...')
    setIsLoading(true)
  }, [])

  const hideLoading = useCallback(() => {
    setIsLoading(false)
    setLoadingText('')
  }, [])

  const withLoading = useCallback(async <T,>(promise: Promise<T>, text?: string): Promise<T> => {
    showLoading(text)
    
    // Add timeout to prevent stuck loading
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error('Loading timeout'))
      }, 10000) // 10 second timeout
    })
    
    try {
      const result = await Promise.race([promise, timeoutPromise])
      return result
    } catch (error) {
      console.error('Loading error:', error)
      throw error
    } finally {
      hideLoading()
    }
  }, [showLoading, hideLoading])

  const value: LoadingContextType = {
    isLoading,
    loadingText,
    showLoading,
    hideLoading,
    withLoading
  }

  return (
    <LoadingContext.Provider value={value}>
      {children}
      {isLoading && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <LoadingPage text={loadingText} />
        </div>
      )}
    </LoadingContext.Provider>
  )
}

export function useLoading() {
  const context = useContext(LoadingContext)
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}
